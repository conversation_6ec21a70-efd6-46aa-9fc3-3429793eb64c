using System.Diagnostics;
using CrashKonijn.Agent.Core;
using CrashKonijn.Agent.Runtime;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Behaviours;

namespace Labsim.AI.GOAP.Actions
{
    [GoapId("Eat-b235695c-727b-41a5-aa66-4757ce65719d")]
    public class EatAction : GoapActionBase<EatAction.Data>
    {
        
        public override IActionRunState Perform(IMonoAgent agent, Data data, IActionContext context)
        {
            
            return ActionRunState.WaitThenComplete(5f);
        }
        public override void Complete(IMonoAgent agent, Data data)
        {
            data.DataBehaviour.pearCount--;
            data.DataBehaviour.hunger = 0f;
        }

    
        public class Data : IActionData
        {
            public ITarget Target { get; set; }
            
         
            [GetComponent]
            public DataBehaviour DataBehaviour { get; set; }
        }
    }
}