using UnityEngine;
using UnityEngine.InputSystem;
using TMPro;
using System;
using PurrNet;

// namespace Labsim
// {
//     public class TerminalInteractor : NetworkBehaviour
//     {
//         [Header("Terminal Interaction Settings")]
//         [SerializeField] private float terminalInteractionDistance = 3f;

//         [Header("UI Feedback")]
//         [SerializeField] private TextMeshProUGUI terminalInteractionPromptText;

//         [Header("Dependencies")]
//         [SerializeField] private Camera m_terminalCamera;

//         private ITerminalInteractable currentTerminalInteractable;
//         private InputAction m_terminalInteractAction;
//         private LabsimInputSystem m_labsimInputSystem;
        
//         private float m_lastDetectionTime;
//         private const float c_detectionInterval = 0.1f;
        
//         private bool m_isActive = false;

//         #region Events
//         public  event Action<GameObject> OnTerminalInteractionSuccess;
//         #endregion

//         #region Properties
//         public bool IsActive => m_isActive;
//         public ITerminalInteractable CurrentTerminalInteractable => currentTerminalInteractable;
//         #endregion

//         #region Unity Lifecycle

//         void Start()
//         {
//             if (m_terminalCamera == null)
//             {
//                 // Try to find camera again if it's null
//                 m_terminalCamera = Camera.main;
//                 if (m_terminalCamera == null)
//                 {
//                     m_terminalCamera = FindFirstObjectByType<Camera>();
//                 }

//                 if (m_terminalCamera == null)
//                 {
//                     Debug.LogError("[TerminalInteractor] Terminal camera is still null in DetectTerminalInteractable");
//                     return;
//                 }
//             }
//         }

//         void Update()
//         {
//             if (!IsOwner || !m_isActive) return;
            
//             if (Time.time - m_lastDetectionTime >= c_detectionInterval)
//             {
//                 DetectTerminalInteractable();
//                 m_lastDetectionTime = Time.time;
//             }
//         }
//         #endregion

//         #region Public Methods
//         public void Initialize(LabsimInputSystem inputSystem, Camera terminalCamera)
//         {
//             m_labsimInputSystem = inputSystem;
//             m_terminalCamera = terminalCamera;

//             if (m_terminalCamera == null)
//             {
//                 Debug.LogWarning("[TerminalInteractor] Terminal camera is null during initialization. Trying to find main camera...");
//                 m_terminalCamera = Camera.main;
//                 if (m_terminalCamera == null)
//                 {
//                     m_terminalCamera = FindFirstObjectByType<Camera>();
//                 }
//             }

//             if (m_labsimInputSystem != null)
//             {
//                 // Get the appropriate terminal interact action based on terminal type
//                 // This will be set by the specific terminal implementation
//                 SetupInputAction();
//             }
//         }

//         public void SetTerminalInteractAction(InputAction interactAction)
//         {
//             // Unsubscribe from previous action if exists
//             if (m_terminalInteractAction != null)
//             {
//                 m_terminalInteractAction.performed -= OnTerminalInteractPerformed;
//             }
            
//             m_terminalInteractAction = interactAction;
            
//             // Subscribe to new action
//             if (m_terminalInteractAction != null)
//             {
//                 m_terminalInteractAction.performed += OnTerminalInteractPerformed;
//                 Debug.Log($"[TerminalInteractor] Terminal interact action set to: {m_terminalInteractAction.name}");
//             }
//         }

//         public void EnableTerminalInteraction()
//         {
//             if (!IsOwner) return;
            
//             m_isActive = true;
//             Debug.Log("[TerminalInteractor] Terminal interaction enabled");
//         }

//         public void DisableTerminalInteraction()
//         {
//             if (!IsOwner) return;
            
//             m_isActive = false;
            
//             // Clear current interactable
//             if (currentTerminalInteractable != null)
//             {
//                 currentTerminalInteractable = null;
//                 UpdateTerminalInteractionPrompt();
//             }
            
//             Debug.Log("[TerminalInteractor] Terminal interaction disabled");
//         }
//         #endregion

//         #region Private Methods
//         private void SetupInputAction()
//         {
//             // This will be called by the terminal to set the appropriate input action
//             // Each terminal type can provide its own TerminalInteract action
//         }

//         private void DetectTerminalInteractable()
//         {
           
            
//             RaycastHit hit;
//             // Use actual mouse position instead of screen center
//             Vector3 mousePosition = Input.mousePosition;
//             Ray ray = m_terminalCamera.ScreenPointToRay(mousePosition);

//             ITerminalInteractable newTerminalInteractable = null;

//             if (Physics.Raycast(ray, out hit, terminalInteractionDistance))
//             {
//                 newTerminalInteractable = hit.collider.GetComponent<ITerminalInteractable>();
//             }

//             if (newTerminalInteractable != currentTerminalInteractable)
//             {
//                 currentTerminalInteractable = newTerminalInteractable;
//                 UpdateTerminalInteractionPrompt();
//             }
//         }

//         private void UpdateTerminalInteractionPrompt()
//         {
//             if (terminalInteractionPromptText == null) return;

//             if (currentTerminalInteractable != null && m_terminalInteractAction != null)
//             {
//                 try
//                 {
//                     string bindingDisplayString = m_terminalInteractAction.GetBindingDisplayString();
//                     string prompt = currentTerminalInteractable.TerminalInteractionPrompt;
//                     terminalInteractionPromptText.text = $"[{bindingDisplayString}] {prompt}";
//                     terminalInteractionPromptText.gameObject.SetActive(true);
//                 }
//                 catch (System.Exception ex)
//                 {
//                     Debug.LogError($"[TerminalInteractor] Exception in UpdateTerminalInteractionPrompt: {ex.Message}");
//                     terminalInteractionPromptText.text = "";
//                     terminalInteractionPromptText.gameObject.SetActive(false);
//                 }
//             }
//             else
//             {
//                 terminalInteractionPromptText.text = "";
//                 terminalInteractionPromptText.gameObject.SetActive(false);
//             }
//         }

//         private void OnTerminalInteractPerformed(InputAction.CallbackContext context)
//         {
//             if (!IsOwner || !m_isActive) return;

//             if (currentTerminalInteractable != null)
//             {
//                 NetworkObject interactableNetworkObject = (currentTerminalInteractable as Component)?.GetComponent<NetworkObject>();
//                 if (interactableNetworkObject != null)
//                 {
//                     RequestTerminalInteractionServerRpc(interactableNetworkObject.NetworkObjectId);
//                 }
//                 else
//                 {
//                     // Handle non-networked terminal interactables
//                     currentTerminalInteractable.TerminalInteract(this.NetworkObject);

//                     // Notify successful interaction for non-networked objects
//                     NotifyTerminalInteractionSuccess(currentTerminalInteractable);
//                 }
//             }
//         }

//         [ServerRpc(RequireOwnership = true)]
//         private void RequestTerminalInteractionServerRpc(ulong targetNetworkObjectId)
//         {
//             if (NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(targetNetworkObjectId, out NetworkObject targetObject))
//             {
//                 float distanceToTarget = Vector3.Distance(transform.position, targetObject.transform.position);

//                 if (distanceToTarget > terminalInteractionDistance + 0.5f)
//                 {
//                     Debug.LogWarning($"Client {OwnerClientId} tried to interact from too far in terminal!");
//                     return;
//                 }

//                 ITerminalInteractable terminalInteractable = targetObject.GetComponent<ITerminalInteractable>();
//                 if (terminalInteractable != null)
//                 {
//                     terminalInteractable.TerminalInteract(this.NetworkObject);

//                     // Notify successful interaction for networked objects
//                     NotifyTerminalInteractionSuccessClientRpc(targetNetworkObjectId);
//                 }
//             }
//         }

//         [ClientRpc]
//         private void NotifyTerminalInteractionSuccessClientRpc(ulong targetNetworkObjectId)
//         {
//             if (NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(targetNetworkObjectId, out NetworkObject targetObject))
//             {
//                 ITerminalInteractable terminalInteractable = targetObject.GetComponent<ITerminalInteractable>();
//                 if (terminalInteractable != null)
//                 {
//                     NotifyTerminalInteractionSuccess(terminalInteractable);
//                 }
//             }
//         }

       
//         private void NotifyTerminalInteractionSuccess(ITerminalInteractable interactable)
//         {
//             GameObject interactableGameObject = (interactable as Component)?.gameObject;
//             if (interactableGameObject != null)
//             {
//                 OnTerminalInteractionSuccess?.Invoke(interactableGameObject);
//             }
//         }
//         #endregion

//         #region Cleanup
//         public override void OnNetworkDespawn()
//         {
//             if (IsOwner && m_terminalInteractAction != null)
//             {
//                 m_terminalInteractAction.performed -= OnTerminalInteractPerformed;
//             }
//             base.OnNetworkDespawn();
//         }
//         #endregion
//     }
// }
