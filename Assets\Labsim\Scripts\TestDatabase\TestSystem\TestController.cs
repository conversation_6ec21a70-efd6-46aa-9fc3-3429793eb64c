// using UnityEngine;
// using System.Collections.Generic; // List için
//
// public class TestController : MonoBehaviour
// {
//     void Start()
//     {
//         // LabDatabaseManager'ın instance'ının hazır olduğundan emin olalım
//         if (LabDatabaseManager.Instance == null)
//         {
//             Debug.LogError("LabDatabaseManager instance is not available!");
//             return;
//         }
//
//         Debug.Log("--- TestController Başlatılıyor ---");
//
//         string patientId1 = "PID001";
//         string patientId2 = "PID002";
//
//         // Hasta 1 için işlemler
//         Debug.Log($"--- Hasta {patientId1} için işlemler ---");
//         LabDatabaseManager.Instance.GetOrCreatePatient(patientId1);
//
//         // Hemogram testi ekle ve parametrelerini güncelle
//         TestResultInstance hemogram = LabDatabaseManager.Instance.AddTestToPatient(patientId1, "HEMOGRAM");
//         if (hemogram != null)
//         {
//             LabDatabaseManager.Instance.UpdateTestParameterValue(patientId1, hemogram.instanceTestId, "WBC", 6.5f);
//             LabDatabaseManager.Instance.UpdateTestParameterValue(patientId1, hemogram.instanceTestId, "PLT", 300); // Int değer
//             LabDatabaseManager.Instance.UpdateTestStatus(patientId1, hemogram.instanceTestId, TestStatus.Completed);
//         }
//
//         // Koagülasyon testi ekle
//         TestResultInstance coag = LabDatabaseManager.Instance.AddTestToPatient(patientId1, "COAGULATION");
//         if (coag != null)
//         {
//             LabDatabaseManager.Instance.UpdateTestParameterValue(patientId1, coag.instanceTestId, "INR", 1); // Int değer
//             LabDatabaseManager.Instance.UpdateTestStatus(patientId1, coag.instanceTestId, TestStatus.InProgress);
//         }
//
//
//         // Hasta 2 için işlemler
//         Debug.Log($"--- Hasta {patientId2} için işlemler ---");
//         LabDatabaseManager.Instance.GetOrCreatePatient(patientId2);
//         TestResultInstance urinalysis = LabDatabaseManager.Instance.AddTestToPatient(patientId2, "URINALYSIS_COMPLETE");
//         if (urinalysis != null)
//         {
//             LabDatabaseManager.Instance.UpdateTestParameterValue(patientId2, urinalysis.instanceTestId, "pH", 5.5f);
//             LabDatabaseManager.Instance.UpdateTestParameterValue(patientId2, urinalysis.instanceTestId, "Comment", "Normal görünümde.");
//             LabDatabaseManager.Instance.UpdateTestStatus(patientId2, urinalysis.instanceTestId, TestStatus.Completed);
//         }
//
//         // Hatalı bir test ID'si ile test eklemeyi dene
//         Debug.Log("--- Hatalı Test ID Denemesi ---");
//         LabDatabaseManager.Instance.AddTestToPatient(patientId1, "OLMAYAN_TEST_ID");
//
//
//         // Hasta 1'in tüm testlerini al ve göster
//         Debug.Log($"--- Hasta {patientId1} Testleri ---");
//         List<TestResultInstance> testsForPatient1 = LabDatabaseManager.Instance.GetTestsForPatient(patientId1);
//         foreach (var test in testsForPatient1)
//         {
//             Debug.Log($"Alınan Test: ID={test.instanceTestId}, Tip={test.testNameID_FK}, Durum={test.status}");
//             Debug.Log("Parametreler:");
//             foreach(var paramEntry in test.parameterResults)
//             {
//                 Debug.Log($"  -> {paramEntry.Key}: {paramEntry.Value} (Tip: {paramEntry.Value?.GetType().Name ?? "null"})");
//             }
//         }
//
//         Debug.Log("--- TestController Tamamlandı ---");
//         Debug.Log($"Veritabanı dosyası şu adreste olmalı: {Application.persistentDataPath}/lab_data.json");
//     }
// }