using UnityEngine;
using PurrNet;
using System;

namespace Labsim
{
    public class PhlebotomyTerminalNetworkController : NetworkBehaviour
    {
        #region Network Variables
        // private NetworkVariable<bool> m_isInUse = new NetworkVariable<bool>(
        //     false,
        //     NetworkVariableReadPermission.Everyone,
        //     NetworkVariableWritePermission.Server);

        // private NetworkVariable<ulong> m_currentClientID = new NetworkVariable<ulong>(
        //     ulong.MaxValue,
        //     NetworkVariableReadPermission.Everyone,
        //     NetworkVariableWritePermission.Server);
        #endregion

        #region Events
        public event Action<ulong> OnTerminalReleased;
        #endregion

        #region Properties
        // public bool IsInUse => m_isInUse.Value;
        // public ulong CurrentClientID => m_currentClientID.Value;
        #endregion

        #region Network Methods
        // public bool IsCurrentlyInUseBy(ulong clientId)
        // {
        //     return IsInUse && CurrentClientID == clientId;
        // }

        // public void SetTerminalInUse(ulong clientId)
        // {
        //     if (IsServer)
        //     {
        //         m_isInUse.Value = true;
        //         m_currentClientID.Value = clientId;
        //     }
        // }

        // [ServerRpc(RequireOwnership = false)]
        // public void SetTerminalInUseServerRpc(ulong clientId)
        // {
        //     if (!m_isInUse.Value)
        //     {
        //         m_isInUse.Value = true;
        //         m_currentClientID.Value = clientId;
        //     }
        // }

        // [ServerRpc(RequireOwnership = false)]
        // public void ReleaseTerminalServerRpc(ServerRpcParams rpcParams = default)
        // {
        //     var clientId = rpcParams.Receive.SenderClientId;
        //     if (clientId == m_currentClientID.Value)
        //     {
        //         m_isInUse.Value = false;
        //         m_currentClientID.Value = ulong.MaxValue;

        //         ClientRpcParams clientRpcParams = new ClientRpcParams
        //         {
        //             Send = new ClientRpcSendParams
        //             {
        //                 TargetClientIds = new ulong[] { clientId }
        //             }
        //         };
        //         ReleaseTerminalClientRpc(clientRpcParams);
        //     }
        // }

        // [ClientRpc]
        // private void ReleaseTerminalClientRpc(ClientRpcParams rpcParams = default)
        // {
        //     OnTerminalReleased?.Invoke(m_currentClientID.Value);
        // }

        // public void ReleaseTerminal()
        // {
        //     ReleaseTerminalServerRpc();
        // }
        // #endregion

        // #region Terminal Usage Methods
        // public bool CanInteract(NetworkObject interactor)
        // {
        //     return !IsInUse || CurrentClientID == interactor.OwnerClientId;
        // }
        #endregion
    }

}
