using CrashKonijn.Agent.Core;
using CrashKonijn.Agent.Runtime;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Behaviours;
using UnityEngine;

namespace Labsim.AI.GOAP.Actions
{
    [GoapId("ReachPhlebotomy-acd42e58-b7f1-482d-9f87-4f5b8a2e1d36")]
    public class ReachPhlebotomyAction : GoapActionBase<ReachPhlebotomyAction.Data>
    {

        public override void Start(IMonoAgent agent, Data data)
        {
            // Validate target exists
            if (data.Target == null)
            {
                Debug.LogWarning("ReachPhlebotomyAction started with null target");
                return;
            }


            // Movement is handled by the agent's event system in AgentMoveBehaviour
            // We don't need to manually set the target - the GOAP system does this for us
            Debug.Log("Starting to move towards phlebotomy target");
        }

        
        private bool UpdateHandRigWeight(Data data, bool transitionToOne)
        {
            // Initialize transition if not started
            if (!data.transitionStarted)
            {
                data.transitionStarted = true;
                data.transitionStartTime = Time.time;
                data.patient.handRig.weight = transitionToOne ? 0f : 1f;
            }
            
            // Calculate progress (0 to 1) based on elapsed time
            float elapsedTime = Time.time - data.transitionStartTime;
            float normalizedProgress = Mathf.Clamp01(elapsedTime / data.transitionDuration);
            
            // If transitioning to 0, invert the progress
            float progress = transitionToOne ? normalizedProgress : 1f - normalizedProgress;
            
            // Apply the new weight value
            data.patient.handRig.weight = progress;
            
            // Return true if transition is complete
            return normalizedProgress >= 1f;
        }

        public override IActionRunState Perform(IMonoAgent agent, Data data, IActionContext context)
        {
            // Handle invalid target
            if (data.Target == null)
            {
                Debug.LogWarning("ReachPhlebotomyAction has null target");
                return ActionRunState.Stop;
            }

            // Check if agent is in range of target
            if (context.IsInRange)
            {
                data.patient.transform.rotation = Quaternion.Euler(0, -104.6f, 0);
                
                // Transition handRig weight from 0 to 1
                UpdateHandRigWeight(data, true);
                
                if (data.patient.isBloodDraw)
                {
                    return ActionRunState.Completed;
                }
            }
            
            // Continue moving to target
            return ActionRunState.Continue;
        }

        public override void Complete(IMonoAgent agent, Data data)
        {
            // Update data state to reflect completion
            if (data.patient != null)
            {
            
                data.movebehavior.terminalCallCount = 0;
                data.movebehavior.navMeshAgent.speed = 1;
                data.patient.handRig.weight = 0f;
                data.patient.isCalled = false;
            }

            
            // Reset transition state for future use
            data.transitionStarted = false;
        }

        public class Data : IActionData
        {
            public ITarget Target { get; set; }
            
            [GetComponent]
            public Patient patient { get; set; }
            
            [GetComponent]
            public AgentMoveBehaviour movebehavior {get; set;}

            // Variables for smooth weight transition
            public bool transitionStarted { get; set; } = false;
            public float transitionStartTime { get; set; } = 0f;
            public float transitionDuration { get; set; } = 1f;
        }
    }
}