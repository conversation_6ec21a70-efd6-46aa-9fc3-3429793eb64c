%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &691849510872091740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 203262155372320486}
  - component: {fileID: 3854512074133625155}
  - component: {fileID: 1340116687216749961}
  - component: {fileID: -1148716795826137326}
  - component: {fileID: 3813403462558578955}
  - component: {fileID: 2476808840141168918}
  - component: {fileID: -4213802974062884592}
  - component: {fileID: -5815195269694006071}
  m_Layer: 0
  m_Name: BlueSampleTube
  m_TagString: Sample
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &203262155372320486
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071069, y: 0, z: 0, w: 0.7071067}
  m_LocalPosition: {x: -1.651, y: 1.058, z: 5.571}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3854512074133625155
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  m_Mesh: {fileID: -6628132967740192280, guid: 7b7660a22db844240b8d77d279fd720e, type: 3}
--- !u!23 &1340116687216749961
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 542d583ef71be424da6754c51cfcbd5c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &-1148716795826137326
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d5a57f767e5e46a458fc5d3c628d0cbb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  GlobalObjectIdHash: 1459355239
  InScenePlacedSourceGlobalObjectIdHash: 0
  DeferredDespawnTick: 0
  Ownership: 1
  AlwaysReplicateAsRoot: 0
  SynchronizeTransform: 1
  ActiveSceneSynchronization: 0
  SceneMigrationSynchronization: 1
  SpawnWithObservers: 1
  DontDestroyWithOwner: 0
  AutoObjectParentSync: 1
  SyncOwnerTransformWhenParented: 1
  AllowOwnerToParent: 0
--- !u!65 &3813403462558578955
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.0016762747, y: 0.0016932636, z: 0.008227293}
  m_Center: {x: 0.0000003069581, y: 0.0000015709084, z: 0.0026645386}
--- !u!114 &2476808840141168918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e96cb6065543e43c4a752faaa1468eb1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Netcode.Runtime::Unity.Netcode.Components.NetworkTransform
  ShowTopMostFoldoutHeaderGroup: 1
  NetworkTransformExpanded: 0
  AutoOwnerAuthorityTickOffset: 1
  PositionInterpolationType: 0
  RotationInterpolationType: 0
  ScaleInterpolationType: 0
  PositionLerpSmoothing: 1
  PositionMaxInterpolationTime: 0.1
  RotationLerpSmoothing: 1
  RotationMaxInterpolationTime: 0.1
  ScaleLerpSmoothing: 1
  ScaleMaxInterpolationTime: 0.1
  AuthorityMode: 0
  TickSyncChildren: 0
  UseUnreliableDeltas: 0
  SyncPositionX: 1
  SyncPositionY: 1
  SyncPositionZ: 1
  SyncRotAngleX: 1
  SyncRotAngleY: 1
  SyncRotAngleZ: 1
  SyncScaleX: 1
  SyncScaleY: 1
  SyncScaleZ: 1
  PositionThreshold: 0.001
  RotAngleThreshold: 0.01
  ScaleThreshold: 0.01
  UseQuaternionSynchronization: 0
  UseQuaternionCompression: 0
  UseHalfFloatPrecision: 0
  InLocalSpace: 0
  SwitchTransformSpaceWhenParented: 0
  Interpolate: 1
  SlerpPosition: 0
--- !u!114 &-4213802974062884592
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::SampleTube
  ShowTopMostFoldoutHeaderGroup: 1
  testInstanceId:
    m_InternalValue: 0
  highlightColor: {r: 0.015686274, g: 1, b: 0.14117648, a: 1}
--- !u!1773428102 &-5815195269694006071
ParentConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691849510872091740}
  m_Enabled: 1
  serializedVersion: 2
  m_Weight: 1
  m_TranslationAtRest: {x: -1.651, y: 1.058, z: 5.571}
  m_RotationAtRest: {x: -90, y: 0, z: 0}
  m_TranslationOffsets: []
  m_RotationOffsets: []
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_AffectRotationX: 1
  m_AffectRotationY: 1
  m_AffectRotationZ: 1
  m_Active: 0
  m_IsLocked: 1
  m_Sources: []
