using CrashKonijn.Goap.Core;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Actions;
using Labsim.AI.GOAP.Goals;
using Labsim.AI.GOAP.Sensors;

namespace Labsim.AI.GOAP.Capabilities
{
    public class ReachPhlebotomyCapability : CapabilityFactoryBase
    {
        public override ICapabilityConfig Create()
        {
            var builder = new CapabilityBuilder("ReachPhlebotomyCapability");

            //Hedef IsCalledToPhlebotomy 0 'a düşürmek
            //o yüzden IsCalledToPhlebotomy düşürecek action'u ararız
            builder.AddGoal<ReachPhlebotomyGoal>()
                .AddCondition<IsCalledToPhlebotomy>(Comparison.SmallerThanOrEqual, 0);

            //IsCalledToPhlebotomy 1'den büyükse veya eşitse çalışır bu action
            builder.AddAction<ReachPhlebotomyAction>()
                .AddCondition<IsCalledToPhlebotomy>(Comparison.GreaterThanOrEqual, 1)
                .AddEffect<IsCalledToPhlebotomy>(EffectType.Decrease)
                .SetTarget<PhlebotomyTarget>();

            
            builder.AddMultiSensor<ReachPhlebotomySensor>();

            return builder.Build();
        }
    }
} 