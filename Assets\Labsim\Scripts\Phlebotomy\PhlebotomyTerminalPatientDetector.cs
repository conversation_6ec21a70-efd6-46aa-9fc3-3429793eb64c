using UnityEngine;
using Labsim;

public class PhlebotomyTerminalPatientDetector : MonoBehaviour
{
    PhlebotomyTerminal phlebotmyTermianl;

    void Start()
    {
        phlebotmyTermianl = GetComponentInParent<PhlebotomyTerminal>();
    }


    void OnTriggerStay(Collider other)
    {
        if (other.gameObject.TryGetComponent(out Patient patient))
        {
            phlebotmyTermianl.isPatientInArea = true;
        }
        else
        {
            phlebotmyTermianl.isPatientInArea = false;
        }
    }

    void OnTriggerExit(Collider other)
    {
        if (other.gameObject.TryGetComponent(out Patient patient))
        {
            phlebotmyTermianl.isPatientInArea = false;
        }
    }
}
