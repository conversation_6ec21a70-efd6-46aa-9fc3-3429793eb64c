using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Reflex.Core;
using Reflex.Attributes;
using Labsim;
using PurrNet;

public class PatientSpawner : NetworkBehaviour
{
    [Header("Spawning Settings")]
    [SerializeField]
    [Tooltip("The patient prefab to spawn. Must have a NetworkObject component and be registered in the NetworkObjectPool.")]
    private GameObject patientPrefab;


    [SerializeField]
    [Tooltip("Whether to spawn patients at random positions around the spawner")]
    private bool useRandomSpawnPosition = false;

    [SerializeField]
    [Tooltip("Radius around the spawner to randomly spawn patients (when random spawn is enabled)")]
    private float randomSpawnRadius = 3f;

    [SerializeField]
    [Tooltip("How often (in seconds) to spawn a new patient")]
    private float spawnFrequency = 10f;

    [SerializeField]
    [Tooltip("Maximum number of patients to spawn (0 = infinite)")]
    private int maxPatientCount = 0;

    [SerializeField]
    [Tooltip("Initial delay in seconds before first patient spawn")]
    private float initialDelay = 5f;

    //public List<NetworkObject> spawnedPatients = new List<NetworkObject>();
    private Coroutine spawnCoroutine;
    private int spawnedPatientsCount = 0;

    [SerializeField] private PatientDataBase patientDataBase;
    [SerializeField] private PatientDataGenerator patientDataGenerator;
    
    [Inject] private ILabDatabaseService _labDatabaseService;

    // public override void OnNetworkSpawn()
    // {
    //     base.OnNetworkSpawn();

    //     if (IsServer)
    //     {
    //         spawnCoroutine = StartCoroutine(SpawnPatientsRoutine());
    //     }
    // }

    // public override void OnNetworkDespawn()
    // {
    //     if (IsServer)
    //     {
    //         if (spawnCoroutine != null)
    //         {
    //             StopCoroutine(spawnCoroutine);
    //             spawnCoroutine = null;
    //         }

    //         // Despawn all patients
    //         foreach (NetworkObject patient in spawnedPatients)
    //         {
    //             if (patient != null && patient.IsSpawned)
    //             {
    //                 patient.Despawn(true);
    //             }
    //         }
            
    //         spawnedPatients.Clear();
    //     }

    //     base.OnNetworkDespawn();
    // }

    private IEnumerator SpawnPatientsRoutine()
    {
        yield return new WaitForSeconds(initialDelay);

        while (maxPatientCount == 0 || spawnedPatientsCount < maxPatientCount)
        {
            //SpawnPatient();
            yield return new WaitForSeconds(spawnFrequency);
        }
    }


    // private void SpawnPatient()
    // {
    //     if (NetworkObjectPool.Singleton == null)
    //     {
    //         //  Debug.LogError($"Cannot spawn patient. NetworkObjectPool Singleton is not available.", this);
    //          return;
    //     }


    //     NetworkObject networkObject = NetworkObjectPool.Singleton.GetNetworkObject(
    //         patientPrefab,
    //         transform.position,
    //         transform.rotation);

    //     if (networkObject != null)
    //     {
    //         spawnedPatients.Add(networkObject);
    //         spawnedPatientsCount++;

    //         string patientFullName;
    //         Gender patientGender;
    //         int patientAge;

    //         if (patientDataGenerator.GetRandomData(out patientFullName, out patientGender, out patientAge))
    //         {
    //             PatientData patientData = patientDataBase.CreatePatient(patientFullName, patientAge, patientGender);
               
    //             Patient patientComponent = networkObject.GetComponent<Patient>();
    //             patientComponent.Initialize(_labDatabaseService);
                
    //             networkObject.Spawn(true);
                
    //             patientComponent.patientId.Value = patientData.patientId;
    //         }
    //         else
    //         {
    //             Debug.LogError("Failed to generate random patient data", this);
    //         }

    //         // Debug.Log($"Server: Successfully spawned patient with NetworkObjectId: {networkObject.NetworkObjectId}", this);
    //     }
    //     else
    //     {
    //         Debug.LogError($"Failed to get NetworkObject from pool for prefab '{patientPrefab.name}'. Is it registered in the pool?", this);
    //     }      
    // }




   
    // public int GetRandomPatientId()
    // {
    //     if (spawnedPatients == null || spawnedPatients.Count == 0)
    //     {
    //         Debug.LogWarning("No patients available to select", this);
    //         return -1;
    //     }

    //     // Select a random index
    //     int randomIndex = Random.Range(0, spawnedPatients.Count);
    //     NetworkObject selectedPatient = spawnedPatients[randomIndex];
        
    //     // Get the patient ID before removing
    //     int patientId = -1;
    //     if (selectedPatient != null && selectedPatient.IsSpawned)
    //     {
    //         Patient patient = selectedPatient.GetComponent<Patient>();
    //         if (patient != null)
    //         {
    //             patientId = patient.patientId.Value;
                
    //             // Remove from the list
    //             spawnedPatients.RemoveAt(randomIndex);
                
    //             // Optionally log success
    //             // Debug.Log($"Selected patient with ID: {patientId} and removed from available list", this);
    //         }
    //         else
    //         {
    //             Debug.LogError("Selected NetworkObject doesn't have a Patient component", this);
    //         }
    //     }
    //     else
    //     {
    //         Debug.LogError("Selected patient is null or not spawned", this);
    //         // Clean up the list by removing the null entry
    //         spawnedPatients.RemoveAt(randomIndex);
    //     }

    //     return patientId;
    // }
}