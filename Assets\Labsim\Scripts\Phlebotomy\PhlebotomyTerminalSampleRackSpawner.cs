using UnityEngine;
using PurrNet;
using System.Collections.Generic;
using Labsim.Phlebotomy;

namespace Labsim
{
    public class PhlebotomyTerminalSampleRackSpawner : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Sample Rack Configuration")]
        [SerializeField] private GameObject sampleRackPrefab;
        [SerializeField] private Vector3 spawnPosition = new Vector3(0.2469f, 0.572f, 0.925f);
        [SerializeField] private Vector3 spawnRotation = new Vector3(-90, -90, 0);
        #endregion

        #region Private Fields
        private List<PhlebotomyTerminalTubeSpawner> tubeSpawners = new List<PhlebotomyTerminalTubeSpawner>();
        #endregion

        #region Public Methods
        // public NetworkObject SpawnSampleRack()
        // {
        //     if (!ValidateSpawnConditions()) return null;

        //     var networkObject = CreateRackNetworkObject();
        //     if (networkObject != null)
        //     {
        //         ConfigureRackTransform(networkObject);
        //         AssignSocketManagerToTubeSpawners(networkObject);
        //     }

        //     return networkObject;
        // }

        public void RegisterTubeSpawner(PhlebotomyTerminalTubeSpawner tubeSpawner)
        {
            if (tubeSpawner != null && !tubeSpawners.Contains(tubeSpawner))
            {
                tubeSpawners.Add(tubeSpawner);
            }
        }

        public void UnregisterTubeSpawner(PhlebotomyTerminalTubeSpawner tubeSpawner)
        {
            tubeSpawners.Remove(tubeSpawner);
        }

        public void ClearTubeSpawners()
        {
            tubeSpawners.Clear();
        }
        #endregion

        #region Private Methods
        private bool ValidateSpawnConditions()
        {
            if (sampleRackPrefab == null)
            {
                Debug.LogError("[SampleRackSpawner] Sample rack prefab is not assigned!");
                return false;
            }

            if (NetworkObjectPool.Singleton == null)
            {
                Debug.LogError("[SampleRackSpawner] NetworkObjectPool Singleton is not available!");
                return false;
            }

            return true;
        }

        // private NetworkObject CreateRackNetworkObject()
        // {
        //     var quaternionRotation = Quaternion.Euler(spawnRotation);
            
        //     var networkObject = NetworkObjectPool.Singleton.GetNetworkObject(
        //         sampleRackPrefab,
        //         spawnPosition,
        //         quaternionRotation
        //     );

        //     if (networkObject != null)
        //     {
        //         networkObject.Spawn(true);
        //         Debug.Log($"[SampleRackSpawner] Successfully spawned {sampleRackPrefab.name}");
        //     }
        //     else
        //     {
        //         Debug.LogError($"[SampleRackSpawner] Failed to get NetworkObject from pool for '{sampleRackPrefab.name}'");
        //     }

        //     return networkObject;
        // }

        // private void ConfigureRackTransform(NetworkObject networkObject)
        // {
        //     var parentNetworkObject = GetComponent<NetworkObject>();
        //     if (parentNetworkObject != null)
        //     {
        //         networkObject.TrySetParent(parentNetworkObject, false);
        //         networkObject.transform.localPosition = spawnPosition;
        //         networkObject.transform.localEulerAngles = spawnRotation;
        //     }
        // }

        // private void AssignSocketManagerToTubeSpawners(NetworkObject spawnedRack)
        // {
        //     if (spawnedRack == null)
        //     {
        //         Debug.LogWarning("[SampleRackSpawner] Cannot assign SocketManager - spawned rack is null");
        //         return;
        //     }

        //     var rackSocketManager = spawnedRack.GetComponent<SocketManager>();
        //     if (rackSocketManager == null)
        //     {
        //         Debug.LogWarning("[SampleRackSpawner] Spawned rack does not have a SocketManager component");
        //         return;
        //     }

        //     int assignedCount = 0;
        //     foreach (var tubeSpawner in tubeSpawners)
        //     {
        //         if (tubeSpawner != null)
        //         {
        //             tubeSpawner.InjectSocketManager(rackSocketManager);
        //             assignedCount++;
        //         }
        //     }

        //     Debug.Log($"[SampleRackSpawner] Assigned SocketManager to {assignedCount} tube spawners");
        // }
        #endregion
    }
}