{"dependencies": {"com.crashkonijn.goap": {"version": "file:com.crashkonijn.goap", "depth": 0, "source": "embedded", "dependencies": {"com.unity.collections": "1.2.4"}}, "com.gustavopsantos.reflex": {"version": "https://github.com/gustavopsantos/reflex.git?path=/Assets/Reflex/#11.0.1", "depth": 0, "source": "git", "dependencies": {}, "hash": "4593b54e2f5faeefa08148e92931196ea5fac6c1"}, "com.unity.ai.navigation": {"version": "2.0.8", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.ai": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.animation.rigging": {"version": "1.3.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.4.1", "com.unity.test-framework": "1.1.24"}, "url": "https://packages.unity.com"}, "com.unity.behavior": {"version": "1.0.9", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.7.2", "com.unity.dt.app-ui": "2.0.0-pre.16", "com.unity.modules.ai": "1.0.0", "com.unity.collections": "2.1.4", "com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.24", "depth": 0, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.cinemachine": {"version": "3.1.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.splines": "2.0.0", "com.unity.modules.imgui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collab-proxy": {"version": "2.8.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.19", "com.unity.mathematics": "1.3.2", "com.unity.test-framework": "1.4.6", "com.unity.nuget.mono-cecil": "1.11.5", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.dt.app-ui": {"version": "2.0.0-pre.16", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.screencapture": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.ide.cursor": {"version": "https://github.com/boxqkrtm/com.unity.ide.cursor.git", "depth": 0, "source": "git", "dependencies": {"com.unity.test-framework": "1.1.9"}, "hash": "2c0153a9bab1abc783e5599fa2d2cc3c69d5e389"}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.23", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.ide.windsurf": {"version": "https://github.com/Asuta/com.unity.ide.windsurf.git", "depth": 0, "source": "git", "dependencies": {"com.unity.test-framework": "1.1.9"}, "hash": "6161accf3e7beab96341813913e714c7e2fb5c5d"}, "com.unity.inputsystem": {"version": "1.14.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.multiplayer.center": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.uielements": "1.0.0"}}, "com.unity.multiplayer.center.quickstart": {"version": "1.0.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0", "com.unity.multiplayer.center": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.multiplayer.playmode": {"version": "1.6.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.nuget.newtonsoft-json": "2.0.2"}, "url": "https://packages.unity.com"}, "com.unity.nuget.mono-cecil": {"version": "1.11.5", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.package-validation-suite": {"version": "0.22.0-preview", "depth": 0, "source": "registry", "dependencies": {"com.unity.nuget.mono-cecil": "0.1.6-preview.2"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.2.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.14", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.universal": {"version": "17.2.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.2.0", "com.unity.shadergraph": "17.2.0", "com.unity.render-pipelines.universal-config": "17.0.3"}}, "com.unity.render-pipelines.universal-config": {"version": "17.0.3", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 0, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.searcher": {"version": "4.9.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "2.1.0", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.2.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.2.0", "com.unity.searcher": "4.9.3"}}, "com.unity.splines": {"version": "2.8.1", "depth": 1, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.imgui": "1.0.0", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.5.1", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.test-framework.performance": {"version": "3.1.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.timeline": {"version": "1.8.8", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "dev.purrnet.purrnet": {"version": "https://github.com/PurrNet/PurrNet.git?path=/Assets/PurrNet#release", "depth": 0, "source": "git", "dependencies": {"com.unity.nuget.mono-cecil": "1.11.4", "com.unity.nuget.newtonsoft-json": "3.2.1"}, "hash": "2c035ec39c0bd912af2a2ada5c5963defde6dcec"}, "com.unity.modules.accessibility": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0", "com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}