%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &285544763844833509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 405598558422698686}
  m_Layer: 0
  m_Name: BloodDrawSitArea
  m_TagString: BloodDrawSitArea
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &405598558422698686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 285544763844833509}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.747, y: -0.178, z: -0.057}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 218855148464120110}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2718147340756618758
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 218855148464120110}
  - component: {fileID: 556916548714533871}
  - component: {fileID: 8425730999681613687}
  - component: {fileID: 1457040613020095720}
  - component: {fileID: 2373107938240382964}
  - component: {fileID: 2200424648562596101}
  m_Layer: 0
  m_Name: PhlebotomyDesk
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &218855148464120110
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2718147340756618758}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.476, z: -0.913}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1281156954158967006}
  - {fileID: 405598558422698686}
  - {fileID: 3803350352177157068}
  - {fileID: 4702268063124703930}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &556916548714533871
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2718147340756618758}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d5a57f767e5e46a458fc5d3c628d0cbb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Netcode.Runtime::Unity.Netcode.NetworkObject
  GlobalObjectIdHash: 2988864562
  InScenePlacedSourceGlobalObjectIdHash: 0
  DeferredDespawnTick: 0
  Ownership: 1
  AlwaysReplicateAsRoot: 0
  SynchronizeTransform: 1
  ActiveSceneSynchronization: 0
  SceneMigrationSynchronization: 1
  SpawnWithObservers: 1
  DontDestroyWithOwner: 0
  AutoObjectParentSync: 1
  SyncOwnerTransformWhenParented: 1
  AllowOwnerToParent: 0
--- !u!114 &8425730999681613687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2718147340756618758}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d603d0dcd967edb4c8e5f5965fabaa7f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::PhlebotomyDesk
  ShowTopMostFoldoutHeaderGroup: 1
  armPrefab: {fileID: 6595493073421943726, guid: 00697a04c3e49b348a6807e2e7612154, type: 3}
--- !u!114 &1457040613020095720
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2718147340756618758}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 826f56a1967f0364391f5c20138c0866, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::SocketManager
  ShowTopMostFoldoutHeaderGroup: 1
  socketPrefab: {fileID: 1029400470205300105, guid: 96ea9bef2fe19ce4ea217cf4a0a246d2, type: 3}
  spawnPositions:
  - {x: 0.341, y: 0.532, z: 0}
  socketRotation: {x: 0, y: 0, z: 0}
  showGizmos: 1
  gizmoColor: {r: 0, g: 1, b: 0, a: 1}
  gizmoSize: 0.1
--- !u!65 &2373107938240382964
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2718147340756618758}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 0.35007596}
  m_Center: {x: 0, y: 0, z: 0.00454998}
--- !u!114 &2200424648562596101
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2718147340756618758}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45e9f2b3c8a4d7e6f9a1b2c3d4e5f6a7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::Labsim.TerminalCameraController
  ShowTopMostFoldoutHeaderGroup: 1
  m_terminalCamera: {fileID: 7352714857377616593}
  m_terminalCameraPriority: 15
  m_terminalCameraTarget: {fileID: 5392775699432469236}
  m_cameraMode: 0
  m_fixedCameraRotation: {x: 0, y: 0, z: 0}
  m_allowCameraModeSwitch: 1
  m_lookSensitivity: 2
  m_pitchClampMin: -60
  m_pitchClampMax: 60
  m_yawClampMin: -125
  m_yawClampMax: 0
  m_smoothRotation: 1
  m_rotationSmoothness: 10
  m_invertYAxis: 0
--- !u!1 &4176224885345930408
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3803350352177157068}
  - component: {fileID: 1727529814030790893}
  - component: {fileID: 9059299424081828507}
  m_Layer: 0
  m_Name: BloodDrawTable
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3803350352177157068
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4176224885345930408}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 218855148464120110}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1727529814030790893
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4176224885345930408}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &9059299424081828507
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4176224885345930408}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5088033059567165411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1281156954158967006}
  - component: {fileID: 7352714857377616593}
  - component: {fileID: 2822771022920054519}
  - component: {fileID: 6054441536260136564}
  m_Layer: 0
  m_Name: PhlebotomyDeskCinemachine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1281156954158967006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5088033059567165411}
  serializedVersion: 2
  m_LocalRotation: {x: 0.004051975, y: -0.97252655, z: 0.23213692, w: 0.016975556}
  m_LocalPosition: {x: 0.229, y: 0.888, z: 0.51}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 218855148464120110}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7352714857377616593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5088033059567165411}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9dfa5b682dcd46bda6128250e975f58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Priority:
    Enabled: 1
    m_Value: 15
  OutputChannel: 1
  StandbyUpdate: 2
  m_StreamingVersion: 20241001
  m_LegacyPriority: 0
  Target:
    TrackingTarget: {fileID: 4702268063124703930}
    LookAtTarget: {fileID: 0}
    CustomLookAtTarget: 0
  Lens:
    FieldOfView: 60.000004
    OrthographicSize: 10
    NearClipPlane: 0.1
    FarClipPlane: 5000
    Dutch: 0
    ModeOverride: 0
    PhysicalProperties:
      GateFit: 2
      SensorSize: {x: 21.946, y: 16.002}
      LensShift: {x: 0, y: 0}
      FocusDistance: 10
      Iso: 200
      ShutterSpeed: 0.005
      Aperture: 16
      BladeCount: 5
      Curvature: {x: 2, y: 11}
      BarrelClipping: 0.25
      Anamorphism: 0
  BlendHint: 0
--- !u!114 &2822771022920054519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5088033059567165411}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 36d1163fa822e8b418a0a603ec078d5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Cinemachine::Unity.Cinemachine.CinemachineHardLockToTarget
  Damping: 0
--- !u!114 &6054441536260136564
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5088033059567165411}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4afa82ae3200b82408d605c3dbdd1e38, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Damping: 0
--- !u!1 &5392775699432469236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4702268063124703930}
  m_Layer: 0
  m_Name: CameraTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4702268063124703930
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5392775699432469236}
  serializedVersion: 2
  m_LocalRotation: {x: 0.004051975, y: -0.97252655, z: 0.23213692, w: 0.016975556}
  m_LocalPosition: {x: 0.229, y: 0.888, z: 0.51}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 218855148464120110}
  m_LocalEulerAnglesHint: {x: 26.85, y: -178, z: 0}
