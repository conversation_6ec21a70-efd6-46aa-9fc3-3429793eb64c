using UnityEngine;

namespace Labsim
{
    public static class TestColorUtility
{
    /// <summary>
    /// TestColor enum değerini Unity Color'a çevirir
    /// </summary>
    /// <param name="testColor">Çevrilecek TestColor değeri</param>
    /// <returns>Unity Color değeri</returns>
    public static Color GetUnityColor(TestColor testColor)
    {
        switch (testColor)
        {
            case TestColor.Red:
                return Color.red;
            case TestColor.Blue:
                return Color.blue;
            case TestColor.Green:
                return Color.green;
            case TestColor.Yellow:
                return Color.yellow;
            case TestColor.Orange:
                return new Color(1f, 0.5f, 0f); // Orange
            case TestColor.Purple:
                return new Color(0.5f, 0f, 1f); // Purple
            case TestColor.Pink:
                return new Color(1f, 0.75f, 0.8f); // Pink
            case TestColor.Cyan:
                return Color.cyan;
            case TestColor.Gray:
                return Color.gray;
            case TestColor.White:
                return Color.white;
            default:
                return Color.white;
        }
    }

    /// <summary>
    /// TestColor enum değerini hex string'e çevirir (CSS için)
    /// </summary>
    /// <param name="testColor">Çevrilecek TestColor değeri</param>
    /// <returns>Hex color string (örn: "#FF0000")</returns>
    public static string GetHexColor(TestColor testColor)
    {
        Color unityColor = GetUnityColor(testColor);
        return ColorUtility.ToHtmlStringRGB(unityColor);
    }

    /// <summary>
    /// TestColor enum değerini CSS uyumlu rgba string'e çevirir
    /// </summary>
    /// <param name="testColor">Çevrilecek TestColor değeri</param>
    /// <param name="alpha">Alpha değeri (0-1 arası)</param>
    /// <returns>RGBA color string (örn: "rgba(255, 0, 0, 1)")</returns>
    public static string GetRgbaColor(TestColor testColor, float alpha = 1f)
    {
        Color unityColor = GetUnityColor(testColor);
        int r = Mathf.RoundToInt(unityColor.r * 255);
        int g = Mathf.RoundToInt(unityColor.g * 255);
        int b = Mathf.RoundToInt(unityColor.b * 255);
        
        return $"rgba({r}, {g}, {b}, {alpha})";
    }
}
}
