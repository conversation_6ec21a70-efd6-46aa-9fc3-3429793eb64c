using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using PurrNet;
using DG.Tweening;
using System.Linq;

namespace Labsim.Phlebotomy
{
    public class PhlebotomyTerminalTubeSpawner : NetworkBehaviour
    {
        [Header("Tube Prefab")]
        [SerializeField] private GameObject tubePrefab;

        [SerializeField] private SocketManager socketManager;
        
        private bool isSpawning = false;
        private bool animationCompleted = false;

        public string TerminalInteractionPrompt => "Spawn Tube";
        

        public void InjectSocketManager(SocketManager socketManager)
        {
            this.socketManager = socketManager;
        }

        

        // public void TerminalInteract(NetworkObject interactorObject)
        // {
        //     if(isSpawning) return;
            
        //     Transform targetSocket = socketManager.ReserveFirstAvailableSocket();
        //     if(targetSocket == null) 
        //     {
        //         Debug.LogWarning("PhlebotomyTerminalTubeSpawner: No available sockets to spawn tube");
        //         return;
        //     }
            
        //     isSpawning = true;
        //     animationCompleted = false;
        //     Vector3 spawnPosition = transform.position;
            
        //     try
        //     {
        //         NetworkObject spawnedTube = NetworkObjectPool.Singleton.GetNetworkObject(tubePrefab, spawnPosition, targetSocket.rotation);
                
        //         if(spawnedTube == null)
        //         {
        //             Debug.LogError("PhlebotomyTerminalTubeSpawner: Failed to get tube from NetworkObjectPool");
        //             HandleSpawnFailure(targetSocket);
        //             return;
        //         }
                
        //         if(spawnedTube.IsSpawned == false)
        //         {
        //             spawnedTube.Spawn();
        //         }
                
        //         if(!spawnedTube.IsSpawned)
        //         {
        //             Debug.LogError("PhlebotomyTerminalTubeSpawner: Failed to spawn NetworkObject");
        //             HandleSpawnFailure(targetSocket);
        //             return;
        //         }
                
        //         spawnedTube.transform.DOMove(targetSocket.position, 1f)
        //             .SetEase(Ease.OutCubic)
        //             .OnComplete(() => {
        //                 animationCompleted = true;
        //                 HandleAnimationComplete(spawnedTube, targetSocket);
        //             })
        //             .OnKill(() => {
        //                 if(!animationCompleted && spawnedTube != null && spawnedTube.IsSpawned)
        //                 {
        //                     Debug.LogWarning("PhlebotomyTerminalTubeSpawner: Animation was killed unexpectedly during active spawn");
        //                     HandleSpawnFailure(targetSocket);
        //                 }
        //             });
        //     }
        //     catch(System.Exception e)
        //     {
        //         Debug.LogError($"PhlebotomyTerminalTubeSpawner: Exception during spawn: {e.Message}");
        //         HandleSpawnFailure(targetSocket);
        //     }
        // }
        
        // private void HandleAnimationComplete(NetworkObject spawnedTube, Transform targetSocket)
        // {
        //     if(spawnedTube == null || targetSocket == null)
        //     {
        //         Debug.LogError("PhlebotomyTerminalTubeSpawner: Null reference in animation complete handler");
        //         HandleSpawnFailure(targetSocket);
        //         return;
        //     }
            
        //     try
        //     {
        //         spawnedTube.transform.rotation = targetSocket.rotation;
                
        //         SampleTube sampleTubeComponent = spawnedTube.GetComponent<SampleTube>();
        //         if(sampleTubeComponent != null)
        //         {
        //             sampleTubeComponent.SetOriginalSpawner(this);
        //             sampleTubeComponent.SetCurrentSocketManager(socketManager);
        //             sampleTubeComponent.SetOriginalPrefab(tubePrefab);
        //         }

        //         bool attachSuccess = socketManager.AttachToSpecificSocket(spawnedTube.gameObject, targetSocket);
        //         if(!attachSuccess)
        //         {
        //             Debug.LogError($"PhlebotomyTerminalTubeSpawner: Failed to attach tube {spawnedTube.name} to socket {targetSocket.name}");
        //             if(spawnedTube.IsSpawned)
        //             {
        //                 spawnedTube.Despawn();
        //             }
        //             socketManager.ReleaseSocketReservation(targetSocket);
        //         }
        //         else
        //         {
        //             Debug.Log($"PhlebotomyTerminalTubeSpawner: Successfully spawned and attached tube to {targetSocket.name}");
        //         }
        //     }
        //     catch(System.Exception e)
        //     {
        //         Debug.LogError($"PhlebotomyTerminalTubeSpawner: Exception in animation complete handler: {e.Message}");
        //         HandleSpawnFailure(targetSocket);
        //     }
        //     finally
        //     {
        //         isSpawning = false;
        //     }
        // }
        
        private void HandleSpawnFailure(Transform targetSocket)
        {
            if(targetSocket != null)
            {
                socketManager.ReleaseSocketReservation(targetSocket);
            }
            isSpawning = false;
            Debug.LogWarning("PhlebotomyTerminalTubeSpawner: Spawn operation failed, released resources");
        }
    }
}