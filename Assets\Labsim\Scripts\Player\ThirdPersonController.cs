using System.Collections.Generic;
using PurrNet;
using UnityEngine;
#if ENABLE_INPUT_SYSTEM 
using UnityEngine.InputSystem;
using UnityEngine.Rendering;
#endif

[RequireComponent(typeof(CharacterController))]
public class ThirdPersonController : NetworkBehaviour
{
    public float MoveSpeed = 2.0f;
    public float SprintSpeed = 5.335f;
    public float RotationSmoothTime = 0.12f;
    public float SpeedChangeRate = 10.0f;
    public bool Grounded = true;
    public float GroundedOffset = -0.14f;
    public float GroundedRadius = 0.28f;
    public LayerMask GroundLayers;

    [SerializeField] private List<Renderer> renderers = new();


    //references
    [SerializeField] private PlayerInputHandler playerInputHandler;


    // player
    private float _speed;
    private float _animationBlend;
    private float _targetRotation = 0.0f;
    private float _rotationVelocity;

    // animation IDs
    private int _animIDSpeed;
    private int _animIDGrounded;
    private int _animIDMotionSpeed;

    private Animator _animator;
    private CharacterController _controller;

    private GameObject _mainCamera;

    private const float _threshold = 0.01f;

    private bool _hasAnimator;




    private void Awake()
    {
        if (_mainCamera == null)
        {
            _mainCamera = GameObject.FindGameObjectWithTag("MainCamera");
        }
        playerInputHandler = GetComponent<PlayerInputHandler>();
    }

    private void Start()
    {
        _hasAnimator = TryGetComponent(out _animator);
        _controller = GetComponent<CharacterController>();
        AssignAnimationIDs();
    }

    protected override void OnSpawned()
    {
        base.OnSpawned();
        enabled = isOwner;
        DisableSelfRender();
    }


    private void Update()
    {
        _hasAnimator = TryGetComponent(out _animator);

        GroundedCheck();
        Move();
    }


    private void AssignAnimationIDs()
    {
        _animIDSpeed = Animator.StringToHash("Speed");
        _animIDGrounded = Animator.StringToHash("Grounded");
        _animIDMotionSpeed = Animator.StringToHash("MotionSpeed");
    }

    private void GroundedCheck()
    {
        // set sphere position, with offset
        Vector3 spherePosition = new Vector3(transform.position.x, transform.position.y - GroundedOffset,
            transform.position.z);
        Grounded = Physics.CheckSphere(spherePosition, GroundedRadius, GroundLayers,
            QueryTriggerInteraction.Ignore);


        // update animator if using character
        if (_hasAnimator)
        {
            _animator.SetBool(_animIDGrounded, Grounded);
        }
    }


    private void Move()
    {
        // Cache input for performance optimization
        Vector2 m_MoveInput = playerInputHandler.playerInput.Player.Move.ReadValue<Vector2>();
        
        float targetSpeed = MoveSpeed;

        // if there is no input, set the target speed to 0
        if (m_MoveInput == Vector2.zero) targetSpeed = 0.0f;

        // a reference to the players current horizontal velocity
        float currentHorizontalSpeed = new Vector3(_controller.velocity.x, 0.0f, _controller.velocity.z).magnitude;

        float speedOffset = 0.1f;
        float inputMagnitude = m_MoveInput.magnitude;

        // accelerate or decelerate to target speed
        if (currentHorizontalSpeed < targetSpeed - speedOffset ||
            currentHorizontalSpeed > targetSpeed + speedOffset)
        {
            // creates curved result rather than a linear one giving a more organic speed change
            // note T in Lerp is clamped, so we don't need to clamp our speed
            _speed = Mathf.Lerp(currentHorizontalSpeed, targetSpeed * inputMagnitude,
                Time.deltaTime * SpeedChangeRate);

            // round speed to 3 decimal places
            _speed = Mathf.Round(_speed * 1000f) / 1000f;
        }
        else
        {
            _speed = targetSpeed;
        }

        _animationBlend = Mathf.Lerp(_animationBlend, targetSpeed, Time.deltaTime * SpeedChangeRate);
        if (_animationBlend < 0.01f) _animationBlend = 0f;

       

        //note: Vector2's != operator uses approximation so is not floating point error prone, and is cheaper than magnitude
        //if there is a move input rotate player when the player is moving
       
       if (m_MoveInput != Vector2.zero)
        {
           
            Vector3 inputDirection = new Vector3(m_MoveInput.x, 0.0f, m_MoveInput.y).normalized;

            _targetRotation = Mathf.Atan2(inputDirection.x, inputDirection.z) * Mathf.Rad2Deg +
                            _mainCamera.transform.eulerAngles.y;
            float rotation = Mathf.SmoothDampAngle(transform.eulerAngles.y, _targetRotation, ref _rotationVelocity,
                RotationSmoothTime);

            // --- YENİ KONTROL KODU BAŞLANGICI ---
            // Hesaplanan rotasyonun NaN olup olmadığını kontrol edelim.
            if (float.IsNaN(rotation))
            {
                Debug.LogError("ThirdPersonController İÇİNDE NaN OLUŞTU! Hesaplanan rotasyon geçersiz. TargetRotation: " + _targetRotation + " Camera Y Angle: " + _mainCamera.transform.eulerAngles.y);
                // Hata oluşursa, bu frame'de rotasyonu atamayıp çıkalım.
                return;
            }
            // --- YENİ KONTROL KODU BİTİŞİ ---

            // rotate to face input direction relative to camera position
            transform.rotation = Quaternion.Euler(0.0f, rotation, 0.0f);
        }


        Vector3 targetDirection = Quaternion.Euler(0.0f, _targetRotation, 0.0f) * Vector3.forward;

        // move the player
        _controller.Move(targetDirection.normalized * (_speed * Time.deltaTime));

        // update animator if using character
        if (_hasAnimator)
        {
            _animator.SetFloat(_animIDSpeed, _animationBlend);
            _animator.SetFloat(_animIDMotionSpeed, inputMagnitude);
        }
    }

    void DisableSelfRender()
    {
        if (isOwner)
        {
            foreach (var render  in renderers )
            {
                render.shadowCastingMode = ShadowCastingMode.ShadowsOnly;
            }
        }
    }
    private void OnFootstep()
    {
        // anim event yüzünden silemiyorum animleri değişince sil
    }
    
    }