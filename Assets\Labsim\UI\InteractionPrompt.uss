.interaction-container {
    position: absolute;
    right: 55%;
    top: 50%;
    translate: 0 -50%;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    padding: 8px 12px;
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.3);
    min-width: 120px;
    transition-duration: 0.2s;
    transition-property: opacity, scale;
    opacity: 0;
    scale: 0.8 0.8;
}

.interaction-container.visible {
    opacity: 1;
    scale: 1 1;
}

.interaction-label {
    font-size: 24px;
    color: rgb(255, 255, 255);
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.2);
}

.interaction-text {
    font-size: 16px;
    color: rgb(200, 200, 200);
    -unity-text-align: middle-left;
    margin-left: 8px;
    -unity-font-style: normal;
    align-self: center;
}

/* Disabled state styles */
.interaction-container.disabled {
    background-color: rgba(100, 0, 0, 0.7);
    border-color: rgba(255, 100, 100, 0.3);
}

.interaction-container.disabled .interaction-label {
    color: rgb(150, 150, 150);
    background-color: rgba(100, 100, 100, 0.1);
    border-color: rgba(150, 150, 150, 0.2);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.interaction-container.disabled .interaction-text {
    color: rgb(120, 120, 120);
    -unity-font-style: italic;
}

/* Negative state styles */
.interaction-container.negative {
    background-color: rgba(139, 0, 0, 0.8);
    border-color: rgba(255, 69, 69, 0.5);
    flex-direction: column;
}

.interaction-container.negative .interaction-label {
    color: rgb(255, 240, 240);
    background-color: rgba(139, 0, 0, 0.2);
    border-color: rgba(255, 69, 69, 0.3);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
}

.interaction-container.negative .interaction-text {
    color: rgb(255, 200, 200);
    -unity-font-style: normal;
    margin-left: 0;
    margin-top: 5px;
    -unity-text-align: middle-center;
}