using CrashKonijn.Goap.Core;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Actions;
using Labsim.AI.GOAP.Goals;
using Labsim.AI.GOAP.Sensors;

namespace Labsim.AI.GOAP.Capabilities
{
    public class ReachBloodDrawCapability : CapabilityFactoryBase
    {
        public override ICapabilityConfig Create()
        {
            var builder = new CapabilityBuilder("ReachBloodDrawCapability");

            //Hedef IsCalledToBloodDraw 0 'a düşürmek
            //o yüzden IsCalledToBloodDraw düşürecek action'u ararız
            builder.AddGoal<ReachBloodDrawGoal>()
                .AddCondition<IsCalledToBloodDraw>(Comparison.SmallerThanOrEqual, 0);

            //IsCalledToBloodDraw 1'den büyükse veya eşitse çalışır bu action
            builder.AddAction<ReachBloodDrawAction>()
                .AddCondition<IsCalledToBloodDraw>(Comparison.GreaterThanOrEqual, 1)
                .AddEffect<IsCalledToBloodDraw>(EffectType.Decrease)
                .SetTarget<BloodDrawTarget>();

            
            builder.AddMultiSensor<ReachBloodDrawSensor>();

            return builder.Build();
        }
    }
} 