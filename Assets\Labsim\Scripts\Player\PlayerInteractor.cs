using UnityEngine;
using UnityEngine.Events;
using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine.InputSystem;
using PurrNet;

public class PlayerInteractor : NetworkBehaviour, IInteractor
{
    [Header("Ray Settings")]
    [SerializeField] private Transform rayOrigin;
    [SerializeField] private float rayDistance = 5f;
    [SerializeField] private LayerMask interactableLayer;

    //References
    public PlayerInputHandler playerInputHandler;
    
    // Properties
    public GameObject GameObject => gameObject;
    public Transform Transform => transform;
    
    // State
    private IInteractable currentTarget;
    private IInteractable previousTarget;
    private bool previousCanInteract;
    
    // Events
    public static Action<IInteractable> OnTargetChanged;
    public static Action<IInteractable> OnInteractionPerformed;
    public static Action<IInteractable,string,bool> OnInteractionHoverEnter;
    public static Action<IInteractable,string,bool> OnInteractionHoverExit;

    protected override void OnSpawned()
    {
        base.OnSpawned();
        enabled = isOwner;

        if (!isOwner) return;

        if (rayOrigin == null)
        {
            Camera mainCam = Camera.main;
            if (mainCam != null)
            {
                rayOrigin = mainCam.transform;
            }
            else
            {
                rayOrigin = transform;
                Debug.LogWarning("No camera found. Using transform as ray origin.");
            }
        }

        // Setup layer
        if (interactableLayer == 0)
        {
            interactableLayer = LayerMask.GetMask("Interactable");
            if (interactableLayer == 0)
            {
                Debug.LogError("Interactable layer not found! Please create an 'Interactable' layer.");
            }
        }
        
        playerInputHandler = GetComponent<PlayerInputHandler>();
        if (playerInputHandler != null)
        {
            // Initialize input with delay to ensure PlayerInputHandler.OnSpawned() has been called
            StartCoroutine(InitializeInputDelayed());
        }
        else
        {
            Debug.LogError("PlayerInputHandler component not found!");
        }
    }

    private System.Collections.IEnumerator InitializeInputDelayed()
    {
        // Wait a frame to ensure PlayerInputHandler has initialized its playerInput
        yield return null;
        
        if (playerInputHandler.playerInput != null)
        {
            playerInputHandler.playerInput.Player.Interact.performed += HandleInteract;
        }
        else
        {
            Debug.LogError("PlayerInput not initialized in PlayerInputHandler!");
        }
    }
    
    void Update()
    {
        if (!isOwner) return;
        PerformRaycast();
        CheckInteractionStateChange();
    }
    
    private void PerformRaycast()
    {
        Ray ray = new Ray(rayOrigin.position, rayOrigin.forward);
        IInteractable detected = null;
        
        if (Physics.Raycast(ray, out RaycastHit hit, rayDistance, interactableLayer))
        {
            detected = hit.collider.GetComponent<IInteractable>();
        }
        
        UpdateTarget(detected);  
    }
    
    private void UpdateTarget(IInteractable newTarget)
    {
        
        if (newTarget == previousTarget) return;
        
       
        if (previousTarget != null)
        {
            previousTarget.OnHoverExit(this);
            string keyDisplay = GetInteractKeyDisplay();
            OnInteractionHoverExit?.Invoke(previousTarget, keyDisplay, previousTarget.CanInteract);
        }
        
        
        if (newTarget != null)
        {
            newTarget.OnHoverEnter(this);
            OnTargetChanged?.Invoke(newTarget);
            string keyDisplay = GetInteractKeyDisplay();
            OnInteractionHoverEnter?.Invoke(newTarget, keyDisplay, newTarget.CanInteract);
        }
        
        currentTarget = newTarget;
        previousTarget = newTarget;
        previousCanInteract = newTarget?.CanInteract ?? false;
    }
    
    private void CheckInteractionStateChange()
    {
        if (currentTarget != null)
        {
            bool currentCanInteract = currentTarget.CanInteract;
            if (currentCanInteract != previousCanInteract)
            {
                previousCanInteract = currentCanInteract;
                
                // CanInteract durumu değişti, UI'ı güncelle
                string keyDisplay = GetInteractKeyDisplay();
                if (currentCanInteract)
                {
                    OnInteractionHoverEnter?.Invoke(currentTarget, keyDisplay, true);
                }
                else
                {
                    OnInteractionHoverEnter?.Invoke(currentTarget, keyDisplay, false);
                }
            }
        }
    }

    void HandleInteract(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            TryInteract();
        }
    }

    
    public void TryInteract()
    {
        if (currentTarget != null && currentTarget.CanInteract)
        {
            currentTarget.OnInteract(this);
            OnInteractionPerformed?.Invoke(currentTarget);
        }
    }
    
    private string GetInteractKeyDisplay()
    {
        if (playerInputHandler?.playerInput?.Player.Interact != null)
        {
            var interactAction = playerInputHandler.playerInput.Player.Interact;
            return interactAction.GetBindingDisplayString(0);
        }
        return "E"; // Default fallback
    }
    
    
}