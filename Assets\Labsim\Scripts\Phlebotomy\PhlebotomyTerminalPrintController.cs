using UnityEngine;
using System;
using Unity.Cinemachine;
using Labsim.Phlebotomy;

namespace Labsim
{
    public class PhlebotomyTerminalPrintController : MonoBehaviour
    {
        #region Dependencies
        [Header("Spawner Dependencies")]
        [SerializeField] private PhlebotomyTerminalBarcodeSpawner barcodeSpawner;
        [SerializeField] private PhlebotomyTerminalSampleRackSpawner sampleRackSpawner;
        
        [Header("Camera Configuration")]
        [SerializeField] private PhlebotomyTerminalCameraController cameraController;
        [SerializeField] private CinemachineCamera terminalMatchCamera;
        #endregion

        #region Events
        public event Action OnPrintStarted;
        public event Action OnPrintCompleted;
        public event Action<string> OnPrintFailed;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeSpawners();
        }
        #endregion

        #region Initialization
        private void InitializeSpawners()
        {
            if (barcodeSpawner == null)
                barcodeSpawner = GetComponent<PhlebotomyTerminalBarcodeSpawner>();
                
            if (sampleRackSpawner == null)
                sampleRackSpawner = GetComponent<PhlebotomyTerminalSampleRackSpawner>();
                
            if (cameraController == null)
                cameraController = GetComponent<PhlebotomyTerminalCameraController>();
        }
        #endregion

        #region Public Methods
        public bool ExecutePrintSequence(string patientId)
        {
            if (string.IsNullOrEmpty(patientId))
            {
                OnPrintFailed?.Invoke("No patient selected");
                return false;
            }

            OnPrintStarted?.Invoke();

            try
            {
                SwitchToFixedCamera();
                
                // if (!SpawnSampleRack())
                // {
                //     OnPrintFailed?.Invoke("Failed to spawn sample rack");
                //     return false;
                // }

                if (!SpawnBarcodesForPatient(patientId))
                {
                    OnPrintFailed?.Invoke("Failed to spawn barcodes");
                    return false;
                }

                OnPrintCompleted?.Invoke();
                return true;
            }
            catch (Exception ex)
            {
                OnPrintFailed?.Invoke($"Print sequence failed: {ex.Message}");
                return false;
            }
        }

        public void RegisterTubeSpawner(PhlebotomyTerminalTubeSpawner tubeSpawner)
        {
            sampleRackSpawner?.RegisterTubeSpawner(tubeSpawner);
        }

        public void UnregisterTubeSpawner(PhlebotomyTerminalTubeSpawner tubeSpawner)
        {
            sampleRackSpawner?.UnregisterTubeSpawner(tubeSpawner);
        }

        public void ClearAllTubeSpawners()
        {
            sampleRackSpawner?.ClearTubeSpawners();
        }
        #endregion

        #region Private Methods
        private void SwitchToFixedCamera()
        {
            if (cameraController != null && terminalMatchCamera != null)
            {
                cameraController.SwitchCamera(terminalMatchCamera, TerminalCameraMode.Fixed);
            }
        }

        // private bool SpawnSampleRack()
        // {
        //     if (sampleRackSpawner == null)
        //     {
        //         Debug.LogError("[PrintController] SampleRackSpawner is not available");
        //         return false;
        //     }

        //     var spawnedRack = sampleRackSpawner.SpawnSampleRack();
        //     return spawnedRack != null;
        // }

        private bool SpawnBarcodesForPatient(string patientId)
        {
            if (barcodeSpawner == null)
            {
                Debug.LogError("[PrintController] BarcodeSpawner is not available");
                return false;
            }

            return barcodeSpawner.SpawnTestBarcodesForPatient(patientId);
        }
        #endregion
    }
}