using DG.Tweening;
using UnityEngine;
using Labsim;
using Unity.Cinemachine;
using UnityEngine.InputSystem;
using PurrNet;

namespace Labsim
{
    public class PhlebotomyDesk : NetworkBehaviour
    {
        #region Phlebotomy Desk Components
        [Header("Phlebotomy Desk Dependencies")]
        [SerializeField] private GameObject armPrefab;
        
        private SocketManager m_socketManager;
        #endregion


        #region Unity Lifecycle
        private void Awake()
        {
            InitializePhlebotomyDeskComponents();
        }

        private void InitializePhlebotomyDeskComponents()
        {
            m_socketManager = GetComponent<SocketManager>();
        }

        #endregion

        #region Phlebotomy Desk Specific Methods
        public void SpawnObjectForSpecificClient(ulong targetClientId, Vector3 position, Quaternion rotation)
        {
            //if (!IsServer) return;

            if (armPrefab == null)
            {
                Debug.LogError("ObjectToSpawnForClientPrefab is not assigned!");
                return;
            }

            GameObject instance = Instantiate(armPrefab, position, rotation);
            //NetworkObject netObj = instance.GetComponent<NetworkObject>();
            
            // netObj.SpawnWithOwnership(targetClientId);
            
            // netObj.TrySetParent(NetworkObject); 
            // instance.transform.localPosition = new Vector3(0.585647643f, 0.694000006f, -0.0069385767f);
            // instance.transform.localRotation = Quaternion.Euler(-234.8f, 0f, 0f);
            // instance.transform.localScale = new Vector3(50,50,50);
            
            Debug.Log($"Server spawned {instance.name} for client {targetClientId} with ownership.");
            
            //LocalVisibilityController visController = instance.GetComponent<LocalVisibilityController>();
            //if (visController == null)
            //{
            //    Debug.Log("no visibility controller");
            //}
        }



        #endregion
    }
}
