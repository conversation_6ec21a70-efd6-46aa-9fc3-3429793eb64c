using System;
using System.Collections.Generic;

namespace Labsim
{
    [System.Serializable]
    public class TestResultInstance
{
    public int instanceTestId { get; set; }    
    public string testNameID_FK { get; set; }    
    public TestStatus status { get; set; }
    public DateTime dateRequested { get; set; }
    public DateTime? dateCompleted { get; set; }  

   
    public Dictionary<string, object> parameterResults { get; set; }

    
    public TestResultInstance()
    {
        this.parameterResults = new Dictionary<string, object>();
    }

    public TestResultInstance(TestDefinitionSO definition, int uniqueInstanceId) : this()
{
    this.instanceTestId = uniqueInstanceId;
    this.testNameID_FK = definition.testNameID;
    this.status = TestStatus.Requested; 
    this.dateRequested = DateTime.UtcNow;

  
    if (definition?.parameters != null)
    {
        foreach (var paramDef in definition.parameters)
        {
            if (paramDef == null) continue;
            switch (paramDef.dataType)
            {
                case FinalParameterDataType.Float:
                    this.parameterResults[paramDef.parameterName] = 0.0f;
                    break;
                case FinalParameterDataType.Int:
                    this.parameterResults[paramDef.parameterName] = 0;
                    break;
                case FinalParameterDataType.String:
                    this.parameterResults[paramDef.parameterName] = string.Empty;
                    break;
                case FinalParameterDataType.Boolean:
                    this.parameterResults[paramDef.parameterName] = false;
                    break;
                default:
                    this.parameterResults[paramDef.parameterName] = null;
                    break;
            }
        }
    }
}

}

[System.Serializable]
public class PatientProfile
{
    public string patientId { get; set; }
    // public string patientName { get; set; } // İsteğe bağlı diğer hasta bilgileri
    public List<TestResultInstance> tests { get; set; }

    public PatientProfile() 
    {
        this.tests = new List<TestResultInstance>();
    }

    public PatientProfile(string id) : this() 
    {
        this.patientId = id;
    }
}


[System.Serializable]
public class LabDatabase
{
    
    public Dictionary<string, PatientProfile> patients { get; set; }
    public int lastTestInstanceNumericId { get; set; }

    public LabDatabase()
    {
        this.patients = new Dictionary<string, PatientProfile>();
        this.lastTestInstanceNumericId = 1000; // VEYA 100,200,999 gibi bir başlangıç değeri
    }
}
}