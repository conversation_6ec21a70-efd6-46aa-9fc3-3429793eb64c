using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using System;

namespace Labsim
{
    public class LabDatabaseService : ILabDatabaseService
    {
        private readonly List<TestDefinitionSO> _availableTestDefinitions;
        private Dictionary<string, TestDefinitionSO> _testDefinitionsMap;
        private LabDatabase _database;
        private string _databasePath;

        public LabDatabaseService(List<TestDefinitionSO> availableTestDefinitions)
        {
            _availableTestDefinitions = availableTestDefinitions ?? new List<TestDefinitionSO>();
            _databasePath = Path.Combine(Application.persistentDataPath, "lab_data.json");
            Debug.Log($"Database path: {_databasePath}");

            InitializeTestDefinitionsMap();
            LoadDatabase();
        }

        private void InitializeTestDefinitionsMap()
        {
            _testDefinitionsMap = new Dictionary<string, TestDefinitionSO>();
            if (_availableTestDefinitions == null)
            {
                Debug.LogError("'availableTestDefinitions' listesi atanmamış!");
                return;
            }

            foreach (var definition in _availableTestDefinitions)
            {
                if (definition != null && !string.IsNullOrEmpty(definition.testNameID))
                {
                    if (!_testDefinitionsMap.ContainsKey(definition.testNameID))
                    {
                        _testDefinitionsMap.Add(definition.testNameID, definition);
                    }
                    else
                    {
                        Debug.LogWarning($"Duplicate TestDefinitionSO ID found: {definition.testNameID}. Only the first one will be loaded into the map.");
                    }
                }
                else if(definition != null)
                {
                    Debug.LogWarning($"A TestDefinitionSO in 'availableTestDefinitions' has a null or empty 'testNameID' and will be ignored: {definition.name}");
                }
            }
        }

        public void LoadDatabase()
        {
            if (File.Exists(_databasePath))
            {
                try
                {
                    string json = File.ReadAllText(_databasePath);
                    _database = JsonConvert.DeserializeObject<LabDatabase>(json);

                    if (_database == null)
                    {
                        Debug.LogWarning("JSON deserialization resulted in a null LabDatabase object. Initializing new database.");
                        _database = new LabDatabase();
                    }
                    else if (_database.patients == null) 
                    {
                        Debug.LogWarning("LabDatabase.patients was null after deserialization. Initializing new patients dictionary.");
                        _database.patients = new Dictionary<string, PatientProfile>();
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error loading database from '{_databasePath}': {ex.Message}. A new database will be created.");
                    _database = new LabDatabase(); 
                }
            }
            else
            {
                Debug.Log($"Database file not found at '{_databasePath}'. A new database will be created.");
                _database = new LabDatabase(); 
            }
        }

        public void SaveDatabase()
        {
            try
            {
                string json = JsonConvert.SerializeObject(_database, Formatting.Indented);
                File.WriteAllText(_databasePath, json);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error saving database to '{_databasePath}': {ex.Message}");
            }
        }

        public PatientProfile GetOrCreatePatient(string patientId)
        {
            if (string.IsNullOrEmpty(patientId))
            {
                Debug.LogError("Patient ID cannot be null or empty.");
                return null;
            }

            if (!_database.patients.TryGetValue(patientId, out PatientProfile patient))
            {
                patient = new PatientProfile(patientId);
                _database.patients.Add(patientId, patient);
                Debug.Log($"New patient created with ID: {patientId}");
            }
            return patient;
        }

        public TestResultInstance AddTestToPatient(string patientId, string testDefinitionID)
        {
            PatientProfile patient = GetOrCreatePatient(patientId);
            if (patient == null) return null;

            if (!_testDefinitionsMap.TryGetValue(testDefinitionID, out TestDefinitionSO definition))
            {
                Debug.LogError($"Test definition with ID '{testDefinitionID}' not found in 'availableTestDefinitions'. Make sure it's added to the LabDatabaseManager in the Inspector and has the correct 'testNameID'.");
                return null;
            }

            if (_database == null)
            {
                Debug.LogError("LabDatabase object is null. Cannot generate new test ID.");
                return null;
            }
            _database.lastTestInstanceNumericId++;
            int newInstanceId = _database.lastTestInstanceNumericId;

            TestResultInstance newTest = new TestResultInstance(definition, newInstanceId);
            patient.tests.Add(newTest);

            SaveDatabase();
            return newTest;
        }

        public List<TestResultInstance> GetTestsForPatient(string patientId)
        {
            if (_database.patients.TryGetValue(patientId, out PatientProfile patient))
            {
                return patient.tests;
            }
            Debug.LogWarning($"Patient with ID '{patientId}' not found.");
            return new List<TestResultInstance>();
        }

        public TestResultInstance GetSpecificTestForPatient(string patientId, int instanceTestId)
        {
            List<TestResultInstance> patientTests = GetTestsForPatient(patientId);
            
            TestResultInstance specificTest = patientTests.FirstOrDefault(test => test.instanceTestId == instanceTestId);

            if (specificTest == null)
            {
                //Debug.LogWarning($"Test with Instance ID '{instanceTestId}' not found for patient '{patientId}'.");
            }
            return specificTest;
        }

        public bool UpdateTestParameterValue(string patientId, int instanceTestId, string parameterName, object value)
        {
            TestResultInstance testInstance = GetSpecificTestForPatient(patientId, instanceTestId);
            if (testInstance == null)
            {
                Debug.LogError($"Could not find test with instance ID '{instanceTestId}' for patient '{patientId}' to update parameter.");
                return false;
            }

            if (!_testDefinitionsMap.TryGetValue(testInstance.testNameID_FK, out TestDefinitionSO definition))
            {
                Debug.LogError($"Could not find TestDefinition for test type {testInstance.testNameID_FK} (Instance ID: {instanceTestId}).");
                return false;
            }

            ParameterDefinition paramDef = definition.parameters.FirstOrDefault(p => p.parameterName == parameterName);
            if (paramDef == null)
            {
                Debug.LogError($"Parameter '{parameterName}' is not defined in the TestDefinition '{definition.displayName}' for test instance '{instanceTestId}'.");
                return false;
            }

            try
            {
                object convertedValue = null;
                switch (paramDef.dataType)
                {
                    case FinalParameterDataType.Float:
                        convertedValue = Convert.ToSingle(value);
                        break;
                    case FinalParameterDataType.Int:
                        convertedValue = Convert.ToInt32(value);
                        break;
                    case FinalParameterDataType.String:
                        convertedValue = Convert.ToString(value);
                        break;
                    case FinalParameterDataType.Boolean:
                        convertedValue = Convert.ToBoolean(value);
                        break;
                    default:
                        Debug.LogError($"Unsupported ParameterDataType: {paramDef.dataType} for parameter '{parameterName}'.");
                        return false;
                }
                testInstance.parameterResults[parameterName] = convertedValue;
                Debug.Log($"Patient '{patientId}', Test '{instanceTestId}', Parameter '{parameterName}' updated to: {convertedValue}");
                SaveDatabase();
                return true;
            }
            catch (FormatException ex)
            {
                Debug.LogError($"Format exception when converting value '{value}' (Type: {value?.GetType().Name}) to {paramDef.dataType} for parameter '{parameterName}'. Error: {ex.Message}");
                return false;
            }
            catch (InvalidCastException ex)
            {
                Debug.LogError($"Invalid cast exception when converting value '{value}' (Type: {value?.GetType().Name}) to {paramDef.dataType} for parameter '{parameterName}'. Error: {ex.Message}");
                return false;
            }
            catch (OverflowException ex)
            {
                Debug.LogError($"Overflow exception when converting value '{value}' (Type: {value?.GetType().Name}) to {paramDef.dataType} for parameter '{parameterName}'. Error: {ex.Message}");
                return false;
            }
        }

        public bool UpdateTestStatus(string patientId, int instanceTestId, TestStatus newStatus)
        {
            TestResultInstance testInstance = GetSpecificTestForPatient(patientId, instanceTestId);
            if (testInstance == null)
            {
                Debug.LogError($"Could not find test with instance ID '{instanceTestId}' for patient '{patientId}' to update status.");
                return false;
            }

            testInstance.status = newStatus;
            if (newStatus == TestStatus.Completed && !testInstance.dateCompleted.HasValue)
            {
                testInstance.dateCompleted = DateTime.UtcNow;
            }
            Debug.Log($"Patient '{patientId}', Test '{instanceTestId}' status updated to: {newStatus}");
            SaveDatabase();
            return true;
        }

        public List<string> GetAvailableTestDefinitionIDs()
        {
            if (_testDefinitionsMap == null || _testDefinitionsMap.Count == 0)
            {
                Debug.LogWarning("Test definitions map is not initialized or empty. Returning empty list of IDs.");
                return new List<string>();
            }
            return _testDefinitionsMap.Keys.ToList(); 
        }

        public TestDefinitionSO GetTestDefinitionByID(string testDefinitionID_FK)
        {
            if (string.IsNullOrEmpty(testDefinitionID_FK))
            {
                Debug.LogWarning("Test Definition ID (FK) to get is null or empty.");
                return null;
            }

            if (_testDefinitionsMap == null)
            {
                Debug.LogError("_testDefinitionsMap is null. Ensure InitializeTestDefinitionsMap() has been called.");
                return null;
            }

            if (_testDefinitionsMap.TryGetValue(testDefinitionID_FK, out TestDefinitionSO definition))
            {
                return definition;
            }

            Debug.LogWarning($"Test definition with ID (FK) '{testDefinitionID_FK}' not found in _testDefinitionsMap.");
            return null; 
        }

        public void Dispose()
        {
            SaveDatabase();
        }
    }

    public enum ResultOutOfRangeStatus
    {
        InRange,       
        Low,           
        High,           
        Abnormal,       
        NotApplicable,  
        Error           
    }

    public static class TestResultEvaluator 
    {
        public static ResultOutOfRangeStatus EvaluateParameter(object resultValue, ParameterDefinition paramDef)
        {
            if (paramDef == null || !paramDef.hasReferenceRange)
            {
                return ResultOutOfRangeStatus.NotApplicable;
            }

            try
            {
                switch (paramDef.dataType) 
                {
                    case FinalParameterDataType.Float:
                        if (resultValue is float || resultValue is double || resultValue is int) 
                        {
                            float val = Convert.ToSingle(resultValue);
                            if (val < paramDef.referenceMin) return ResultOutOfRangeStatus.Low;
                            if (val > paramDef.referenceMax) return ResultOutOfRangeStatus.High;
                            return ResultOutOfRangeStatus.InRange;
                        }
                        break;

                    case FinalParameterDataType.Int:
                        if (resultValue is int || resultValue is long || resultValue is float || resultValue is double) 
                        {
                            int val;
                            if (resultValue is float || resultValue is double)
                            {
                                val = Convert.ToInt32(Math.Round(Convert.ToDouble(resultValue)));
                            }
                            else
                            {
                                val = Convert.ToInt32(resultValue);
                            }

                            if (val < Mathf.RoundToInt(paramDef.referenceMin)) return ResultOutOfRangeStatus.Low;
                            if (val > Mathf.RoundToInt(paramDef.referenceMax)) return ResultOutOfRangeStatus.High; 
                            return ResultOutOfRangeStatus.InRange;
                        }
                        break;

                    case FinalParameterDataType.String:
                        if (resultValue is string)
                        {
                            string val = (string)resultValue;
                            
                            if (!string.IsNullOrEmpty(paramDef.referenceText) && val.Equals(paramDef.referenceText, StringComparison.OrdinalIgnoreCase))
                            {
                                return ResultOutOfRangeStatus.InRange; 
                            }
                            return ResultOutOfRangeStatus.Abnormal; 
                        }
                        break;

                    case FinalParameterDataType.Boolean:
                        if (resultValue is bool)
                        {
                            bool val = (bool)resultValue;
                            bool expectedVal;
                            if (bool.TryParse(paramDef.referenceText, out expectedVal))
                            {
                                return val == expectedVal ? ResultOutOfRangeStatus.InRange : ResultOutOfRangeStatus.Abnormal;
                            }
                            else
                            {
                                Debug.LogWarning($"Parameter '{paramDef.parameterName}': Boolean reference text '{paramDef.referenceText}' is not a valid boolean.");
                                return ResultOutOfRangeStatus.Error; 
                            }
                        }
                        break;
                }
                Debug.LogWarning($"Parameter '{paramDef.parameterName}': Result value type '{resultValue?.GetType().Name}' does not match parameter data type '{paramDef.dataType}' for range evaluation or unhandled type.");
                return ResultOutOfRangeStatus.Error;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error evaluating parameter '{paramDef.parameterName}': {ex.Message}");
                return ResultOutOfRangeStatus.Error;
            }
        }
    }
}