using System;
using CrashKonijn.Agent.Core;
using CrashKonijn.Agent.Runtime;
using UnityEngine;
using UnityEngine.AI;

namespace Labsim.AI.GOAP.Behaviours
{
    [RequireComponent(typeof(NavMeshAgent))]
    public class AgentMoveBehaviour : MonoBehaviour
    {
        private AgentBehaviour agent;
        private ITarget currentTarget;
        private bool shouldMove;
        public NavMeshAgent navMeshAgent;
        private Patient patient;
        
        [SerializeField]
        private Animator patientAnimator;

        
        public int terminalCallCount = 0;

        private void Awake()
        {
            agent = GetComponent<AgentBehaviour>();
            navMeshAgent = GetComponent<NavMeshAgent>();
            patientAnimator = GetComponent<Animator>();
            patient = GetComponent<Patient>();
        }

        private void OnEnable()
        {
            this.agent.Events.OnTargetInRange += this.OnTargetInRange;
            this.agent.Events.OnTargetChanged += this.OnTargetChanged;
            this.agent.Events.OnTargetNotInRange += this.TargetNotInRange;
            this.agent.Events.OnTargetLost += this.TargetLost;
            PhlebotomyTerminal.OnPhlebotomyTerminalCall -= OnTerminalCall;
            PhlebotomyTerminal.OnPhlebotomyTerminalCall += OnTerminalCall;
        }

   

        private void OnDisable()
        {
            this.agent.Events.OnTargetInRange -= this.OnTargetInRange;
            this.agent.Events.OnTargetChanged -= this.OnTargetChanged;
            this.agent.Events.OnTargetNotInRange -= this.TargetNotInRange;
            this.agent.Events.OnTargetLost -= this.TargetLost;
            PhlebotomyTerminal.OnPhlebotomyTerminalCall -= OnTerminalCall;
        }
        
        private void TargetLost()
        {
            this.currentTarget = null;
            this.shouldMove = false;
            if (this.navMeshAgent.isOnNavMesh) this.navMeshAgent.isStopped = true;
        }

        private void OnTargetInRange(ITarget target)
        {
            this.shouldMove = false;
            if (this.navMeshAgent.isOnNavMesh) this.navMeshAgent.isStopped = true;
        }

        private void OnTargetChanged(ITarget target, bool inRange)
        {
            this.currentTarget = target;
            this.shouldMove = !inRange;
            
            if (!inRange && target != null) 
            {
                Vector3 targetPosition = new Vector3(target.Position.x, this.transform.position.y, target.Position.z);
                if (this.navMeshAgent.isOnNavMesh) 
                {
                    this.navMeshAgent.SetDestination(targetPosition);
                    this.navMeshAgent.isStopped = false;
                }
            }
            else
            {
                if (this.navMeshAgent.isOnNavMesh) 
                {
                    this.navMeshAgent.isStopped = true;
                }
            }
        }

        private void TargetNotInRange(ITarget target)
        {
            this.shouldMove = true;
            
            if (target != null)
            {
                Vector3 targetPosition = new Vector3(target.Position.x, this.transform.position.y, target.Position.z);
                 if (this.navMeshAgent.isOnNavMesh) 
                 {
                    this.navMeshAgent.SetDestination(targetPosition);
                    this.navMeshAgent.isStopped = false;
                 }
            }
        }
        
        
        private void OnTerminalCall(ulong arg1, int targetAgentId, bool arg3, Transform arg4)
        {
            // if (targetAgentId != patient.patientId.Value)
            // {
            //     return;
            // }
            // terminalCallCount++;
            // if (terminalCallCount>1)
            // {
            //     navMeshAgent.speed = 4f;
            // }
        }

        public void Update()
        {
            if (patientAnimator != null)
            {
                bool isActuallyMoving = navMeshAgent.velocity.magnitude > 0.3f;
                patientAnimator.SetBool("IsWalking", isActuallyMoving);
                bool isRunning = navMeshAgent.velocity.magnitude > 2f;
                patientAnimator.SetBool("IsRunning", isRunning);
            }
            
            if (this.agent.IsPaused)
            {
                if (this.navMeshAgent.isOnNavMesh) 
                    this.navMeshAgent.isStopped = true;
                return;
            }

            if (!this.shouldMove)
            {
                if (this.navMeshAgent.isOnNavMesh)
                    this.navMeshAgent.isStopped = true;
                return;
            }
            
            if (this.currentTarget == null)
            {
                if (this.navMeshAgent.isOnNavMesh) 
                    this.navMeshAgent.isStopped = true;
                return;
            }

           
            
            // Update içinde sürekli hedef ayarlamak yerine olay bazlı ayarlama daha verimli
            // Ancak hedef dinamik olarak değişiyorsa burası gerekli olabilir.
            // Şimdilik yoruma alalım, sorun devam ederse açabiliriz.
            /*
            Vector3 targetPosition = new Vector3(this.currentTarget.Position.x, this.transform.position.y, this.currentTarget.Position.z);
            
            if ((targetPosition - this.navMeshAgent.destination).sqrMagnitude > 0.1f)
            {
                Debug.Log($"[Move] Update: Re-setting destination to {targetPosition}", gameObject); // Update logu (opsiyonel)
                this.navMeshAgent.SetDestination(targetPosition);
            }
            */
        }
    }
}