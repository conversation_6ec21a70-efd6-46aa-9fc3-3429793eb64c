using UnityEngine;
using PurrNet;
using System.Collections.Generic;
using Reflex.Attributes;

namespace Labsim
{
    public class PhlebotomyTerminalBarcodeSpawner : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Barcode Configuration")]
        [SerializeField] private GameObject testBarcodePrefab;
        [SerializeField] private Transform barcodeSpawnRoot;
        [SerializeField] private Vector3 barcodeSpawnStartPosition = new Vector3(0.5f, 0.6f, 0.9f);
        [SerializeField] private Vector3 barcodeSpawnOffset = new Vector3(0, -0.05f, 0);
        #endregion

        #region Dependencies
        private ILabDatabaseService labDatabaseService;
        
        [Inject]
        private void Construct(ILabDatabaseService labDatabaseService)
        {
            this.labDatabaseService = labDatabaseService;
        }
        #endregion

        #region Public Methods
        public bool SpawnTestBarcodesForPatient(string patientId)
        {
            if (!ValidateSpawnConditions(patientId)) return false;

            var patientTests = labDatabaseService?.GetTestsForPatient(patientId);
            if (patientTests == null || patientTests.Count == 0)
            {
                Debug.LogWarning($"[BarcodeSpawner] No tests found for patient {patientId}");
                return false;
            }

            SpawnBarcodes(patientTests);
            return true;
        }
        #endregion

        #region Private Methods
        private bool ValidateSpawnConditions(string patientId)
        {
            if (testBarcodePrefab == null)
            {
                Debug.LogError("[BarcodeSpawner] TestBarcode prefab is not assigned!");
                return false;
            }
            
            if (NetworkObjectPool.Singleton == null)
            {
                Debug.LogError("[BarcodeSpawner] NetworkObjectPool Singleton is not available!");
                return false;
            }
            
            if (labDatabaseService == null)
            {
                Debug.LogError("[BarcodeSpawner] LabDatabaseService is null!");
                return false;
            }
            
            if (string.IsNullOrEmpty(patientId))
            {
                Debug.LogWarning("[BarcodeSpawner] No patient selected for barcode spawning");
                return false;
            }

            return true;
        }

        private void SpawnBarcodes(List<TestResultInstance> patientTests)
        {
            var spawnConfig = CalculateSpawnConfiguration(patientTests.Count);
            Transform parentTransform = barcodeSpawnRoot ?? transform;

            for (int i = 0; i < patientTests.Count; i++)
            {
                //SpawnSingleBarcode(patientTests[i], i, spawnConfig, parentTransform);
            }
        }

        private SpawnConfiguration CalculateSpawnConfiguration(int testCount)
        {
            float offsetY = Mathf.Abs(barcodeSpawnOffset.y);
            float totalHeight = (testCount - 1) * offsetY;
            float startY = -totalHeight / 2f;

            return new SpawnConfiguration
            {
                StartY = startY,
                OffsetY = offsetY,
                Rotation = Quaternion.Euler(-90, -90, 0)
            };
        }

        // private void SpawnSingleBarcode(TestResultInstance test, int index, SpawnConfiguration config, Transform parent)
        // {
        //     var networkObject = NetworkObjectPool.Singleton.GetNetworkObject(
        //         testBarcodePrefab,
        //         Vector3.zero,
        //         Quaternion.identity
        //     );

        //     if (networkObject == null)
        //     {
        //         Debug.LogError($"[BarcodeSpawner] Failed to get NetworkObject from pool for '{testBarcodePrefab.name}'");
        //         return;
        //     }

        //     networkObject.Spawn(true);
        //     SetBarcodeTransform(networkObject, index, config, parent);
        //     SetBarcodeData(networkObject, test);
        // }

        // private void SetBarcodeTransform(NetworkObject networkObject, int index, SpawnConfiguration config, Transform parent)
        // {
        //     float yPosition = config.StartY + index * config.OffsetY;
        //     Vector3 localPosition = new Vector3(0, yPosition, 0);

        //     if (parent.TryGetComponent<NetworkObject>(out _))
        //     {
        //         networkObject.TrySetParent(parent, false);
        //         networkObject.transform.localPosition = localPosition;
        //         networkObject.transform.localEulerAngles = config.Rotation.eulerAngles;
        //     }
        //     else
        //     {
        //         networkObject.transform.SetParent(null, false);
        //         networkObject.transform.position = parent.position + localPosition;
        //         networkObject.transform.eulerAngles = config.Rotation.eulerAngles;
        //     }
        // }

        // private void SetBarcodeData(NetworkObject networkObject, TestResultInstance test)
        // {
        //     var testBarcode = networkObject.GetComponent<TestBarcode>();
        //     if (testBarcode != null)
        //     {
        //         testBarcode.SetTestData(test);
        //     }
        //     else
        //     {
        //         Debug.LogError("[BarcodeSpawner] Spawned object does not have TestBarcode component!");
        //     }
        // }
        #endregion

        #region Helper Structures
        private struct SpawnConfiguration
        {
            public float StartY;
            public float OffsetY;
            public Quaternion Rotation;
        }
        #endregion
    }
}