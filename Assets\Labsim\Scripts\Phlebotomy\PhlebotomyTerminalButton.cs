using UnityEngine;
using PurrNet;
using Labsim;

public class PhlebotomyTerminalButton : NetworkBehaviour
{
    PhlebotomyTerminal m_phlebotomyTerminal;

    public string TerminalInteractionPrompt => "Button";

    void Start()
    {
        m_phlebotomyTerminal = GetComponentInParent<PhlebotomyTerminal>();
    }
    
    // void OnButtonPressed()
    // {
    //     if (!IsOwner) return;
    //     Debug.Log("Button pressed in terminal");
    //     m_phlebotomyTerminal.PhlebotomyTerminalCall(OwnerClientId);
    // }

    // public void TerminalInteract(NetworkObject interactorObject)
    // {
       
    //     OnButtonPressed();
    // }
}
