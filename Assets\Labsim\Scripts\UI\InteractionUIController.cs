using System;
using UnityEngine;
using UnityEngine.UIElements;
using PurrNet;

public class InteractionUIController : NetworkBehaviour
{


    [Header("UI References")]
    [SerializeField] private UIDocument m_UIDocument;
    [SerializeField] private VisualTreeAsset m_InteractionPromptTemplate;

    [Header("Settings")]
    [SerializeField] private float m_FadeSpeed = 5f;

    private VisualElement m_InteractionContainer;
    private Label m_InteractionLabel;
    private Label m_InteractionText;
    private bool m_IsVisible = false;


    void Start()
    {
        InitializeUI();
        SubscribeToEvents();
    }

    private void InitializeUI()
    {
        if (m_UIDocument == null)
            m_UIDocument = GetComponent<UIDocument>();

        if (m_UIDocument == null)
        {
            Debug.LogError("InteractionUIController: UIDocument component not found!");
            return;
        }

        var root = m_UIDocument.rootVisualElement;

        try
        {
            // Load the interaction prompt template if it exists
            if (m_InteractionPromptTemplate != null)
            {
                var promptElement = m_InteractionPromptTemplate.Instantiate();
                root.Add(promptElement);
            }

            // Get UI elements
            m_InteractionContainer = root.Q<VisualElement>("InteractionContainer");
            m_InteractionLabel = root.Q<Label>("InteractionLabel");
            m_InteractionText = root.Q<Label>("InteractionText");

            if (m_InteractionContainer == null)
            {
                Debug.LogError("InteractionUIController: Could not find InteractionContainer element!");
                return;
            }

            // Initialize as hidden
            HidePrompt();
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[InteractionUIController] Exception during UI initialization: {ex.Message}");
        }
    }

    private void SubscribeToEvents()
    {
        PlayerInteractor.OnInteractionHoverEnter += OnInteractionHoverEnter;
        PlayerInteractor.OnInteractionHoverExit += OnInteractionHoverExit;
    }

    private void OnInteractionHoverEnter(IInteractable ınteractable, string interactionKey, bool canInteract)
    {
        if (canInteract)
        {
            ShowPrompt(interactionKey, ınteractable.InteractionPrompt);
        }
        else
        {
            ShowNegativePrompt(ınteractable.InteractionBlockReason);
        }
    }

    private void OnInteractionHoverExit(IInteractable ınteractable, string interactionKey,bool canInteract)
    {
        HidePrompt();
    }

   

    public void ShowPrompt(string _interactionKey, string _interactionText, bool _canInteract = true)
    {
        if (m_InteractionContainer == null) return;

        m_InteractionLabel.text = _interactionKey;
        m_InteractionText.text = _interactionText;

        // Show interaction label in normal state
        if (m_InteractionLabel != null)
        {
            m_InteractionLabel.style.display = DisplayStyle.Flex;
        }

        // Remove previous state classes
        m_InteractionContainer.RemoveFromClassList("disabled");
        m_InteractionContainer.RemoveFromClassList("negative");

        // Add appropriate class based on interaction state
        if (!_canInteract)
        {
            m_InteractionContainer.AddToClassList("disabled");
        }

        m_InteractionContainer.AddToClassList("visible");
        m_IsVisible = true;
    }


    public void ShowNegativePrompt(string _interactionText)
    {
        if (m_InteractionContainer == null) return;

        m_InteractionText.text = _interactionText;
        
        // Hide interaction label in negative state
        if (m_InteractionLabel != null)
        {
            m_InteractionLabel.style.display = DisplayStyle.None;
        }

        // Remove previous state classes
        m_InteractionContainer.RemoveFromClassList("disabled");
        m_InteractionContainer.RemoveFromClassList("negative");

        m_InteractionContainer.AddToClassList("visible");
        m_InteractionContainer.AddToClassList("negative");
        m_IsVisible = true;
    }

    public void HidePrompt()
    {
        if (m_InteractionContainer == null) return;

        m_InteractionContainer.RemoveFromClassList("visible");
        m_InteractionContainer.RemoveFromClassList("disabled");
        m_InteractionContainer.RemoveFromClassList("negative");
        m_IsVisible = false;
    }

    public void UpdatePromptText(string _interactionKey, string _interactionText)
    {
        if (m_IsVisible)
        {
            m_InteractionLabel.text = _interactionKey;
            m_InteractionText.text = _interactionText;
        }
    }

    void OnDestroy()
    {
        PlayerInteractor.OnInteractionHoverEnter -= OnInteractionHoverEnter;
        PlayerInteractor.OnInteractionHoverExit -= OnInteractionHoverExit;
    }
}