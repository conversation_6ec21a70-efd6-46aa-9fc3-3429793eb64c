using CrashKonijn.Goap.Core;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Actions;
using Labsim.AI.GOAP.Goals;

namespace Labsim.AI.GOAP.Capabilities
{
    public class EatCapability : CapabilityFactoryBase
    {
        public override ICapabilityConfig Create()
        {
            var builder = new CapabilityBuilder("EatCapability");

            builder.AddGoal<EatGoal>()
                .AddCondition<Hunger>(Comparison.SmallerThanOrEqual, 0);
            
            builder.AddAction<EatAction>()
                .AddCondition<PearCount>(Comparison.GreaterThanOrEqual, 1)
                .AddEffect<Hunger>(EffectType.Decrease)
                .SetRequiresTarget(false);

            return builder.Build();
        }
    }
} 