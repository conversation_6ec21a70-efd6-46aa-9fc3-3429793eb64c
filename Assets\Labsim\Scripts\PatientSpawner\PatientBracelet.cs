using System;
using Labsim;
using UnityEngine;
using PurrNet;

public class PatientBracelet : MonoBehaviour
{

    public int PatientId;

    public Action OnBraceletInteract;

    public string InteractionPrompt => "PatientBracelet";
    public string TerminalInteractionPrompt => "Scan Bracelet";


    // public void TerminalInteract(NetworkObject interactorObject)
    // {
    //     PhlebotomyTerminal terminal = interactorObject.GetComponent<PhlebotomyTerminal>();
    //     if (terminal != null && terminal is PhlebotomyTerminal)
    //     {
    //         OnBraceletInteract?.Invoke();
    //         Debug.Log("Patient bracelet scanned in PhlebotomyTerminal");
    //     }
    // }


}
