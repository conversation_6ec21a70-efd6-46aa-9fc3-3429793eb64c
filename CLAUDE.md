# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LabSim is a Unity 6000.2.0f1 multiplayer medical laboratory simulation game built for medical training. The project simulates laboratory procedures like phlebotomy (blood drawing) with networked multiplayer support.

## Networking Architecture

- **Networking Framework**: PurrNet (custom networking solution)
  - Uses `PurrNet.NetworkBehaviour` instead of Unity Netcode's `NetworkBehaviour`
  - Network prefabs are managed via `Assets/NetworkPrefabs.asset`
  - PurrNet settings configured in `ProjectSettings/PurrNetSettings.asset`

- **Network Components**: All networked GameObjects use PurrNet components and inherit from `PurrNet.NetworkBehaviour`
- **Player Networking**: Player controller (`ThirdPersonController`) uses PurrNet for multiplayer synchronization

## Dependency Injection

- **DI Container**: Reflex DI framework (`com.gustavopsantos.reflex`)
- **Project-level DI**: `ProjectInstaller.cs` - handles core services like `ILabDatabaseService`
- **Scene-level DI**: `GameInstaller.cs` - handles scene-specific bindings
- **Service Access**: Database service available via `GameInstaller.DatabaseService` static property

## AI System

- **AI Framework**: CrashKonijn GOAP (Goal-Oriented Action Planning)
- **AI Structure**:
  - `Actions/` - Discrete AI actions (EatAction, IdleAction, etc.)
  - `Goals/` - AI objectives (EatGoal, IdleGoal, etc.) 
  - `Sensors/` - Environmental awareness (IdleTargetSensor, PearSensor, etc.)
  - `Behaviours/` - Core AI behaviors (`AgentMoveBehaviour` with NavMeshAgent integration)
  - `WorldKeys/` - Global state variables (Hunger, IsIdle, etc.)
  - `TargetKeys/` - Target identification system

## Test System Architecture

- **Database Service**: `LabDatabaseService` implements `ILabDatabaseService`
- **Test Definitions**: ScriptableObjects define medical tests (Hemogram, Coagulation, Urinalysis)
- **Data Persistence**: JSON-based patient data storage in `Application.persistentDataPath`
- **Patient System**: Procedurally generated patient data with medical test parameters

## Input System

- **Input Framework**: Unity Input System
- **Input Asset**: `LabsimInputSystem.inputactions`
- **Handler**: `PlayerInputHandler` processes player input

## Core Systems

### Phlebotomy System
- Terminal-based interaction system for blood draw procedures
- UI built with UI Toolkit (UXML/USS files in `Terminal/`)
- Barcode scanning simulation
- Sample tube management with color-coded test types

### Interaction System
- Interface-based interaction (`IInteractable`, `ITerminalInteractable`)
- Drag and drop system (`IDragable`, `IDropable`)
- Socket-based object placement system

### Audio System
- Medical equipment sound effects (buzzer calls, alerts)
- Located in `SFX/` directory

## Development Commands

Unity Editor is the primary development environment. No specific build/test commands are configured in the project.

## Code Conventions

Follow the established naming conventions from `.cursorrules`:
- Variables: `m_VariableName`
- Constants: `c_ConstantName` 
- Statics: `s_StaticName`
- Methods: `MethodName()`
- Arguments: `_argumentName`

## Key Architectural Patterns

1. **Component-Based Architecture**: Heavy use of Unity's component system
2. **Service Layer**: Dependency injection for core services
3. **Interface Segregation**: Clean interfaces for interactions, drag/drop, terminals
4. **ScriptableObject Data**: Configuration and test definitions stored as ScriptableObjects
5. **GOAP AI**: Goal-oriented planning for intelligent patient behavior
6. **Network Authority**: PurrNet handles multiplayer state synchronization

## Important Notes

- Project uses Unity 6000.2.0f1 with URP (Universal Render Pipeline)
- Multiplayer development should test with ParrelSync for multiple Unity instances
- AI patients use NavMesh for movement and GOAP for decision making
- Medical simulation focuses on laboratory procedures and protocols