using Labsim;
using UnityEngine;
using System;

public class PhlebotomyTerminalDependencyInjector : MonoBehaviour
{
    PhlebotomyTerminalCameraController phlebotomyTerminalCameraController;

    void Awake()
    {

        phlebotomyTerminalCameraController = GetComponent<PhlebotomyTerminalCameraController>();
        PhlebotomyTerminalInteractController.OnTerminalInteracted += HandleTerminalInteracted;
    }


    private void HandleTerminalInteracted(IInteractor interactor)
    {
       phlebotomyTerminalCameraController.playerInputHandler = interactor.Transform.GetComponent<PlayerInputHandler>();
    }
}
