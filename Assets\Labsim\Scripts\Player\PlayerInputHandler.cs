using UnityEngine;
using PurrNet;

public class PlayerInputHandler : NetworkBehaviour
{

    public PlayerInput playerInput;

    protected override void OnSpawned()
    {
        base.OnSpawned();
        enabled = isOwner;
        if (isOwner)
        {
            playerInput = new PlayerInput();
            playerInput.Player.Enable();
        }
    }

    public void CursorLock()
    {
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    public void CursorUnlock()
    {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }
    
    

}
