namespace Labsim
{
    public enum TestCategory
    {
        Hematology,
        Coagulation,
        Urinalysis,
        Biochemistry,
        Microbiology,
        Other
    }

    public enum TestStatus
    {
        Requested,       // İstendi
        SampleCollected, // Numune Alındı
        InProgress,      // Çalışılıyor
        Completed,       // Tamamlandı
        Cancelled        // İptal Edildi
    }

    public enum FinalParameterDataType
    {
        Float,
        Int,
        String,
        Boolean // <PERSON>steğe bağlı, "Nitrite" gibi parametreler için kullanılabilir
    }

    public enum TestColor
    {
        Red,        // Kırmızı
        Blue,       // Ma<PERSON>,      // Yeşil
        Yellow,     // Sarı
        Orange,     // Turuncu
        Purple,     // Mor
        Pink,       // Pembe
        Cyan,       // Camgöbeği
        Gray,       // <PERSON><PERSON>       // <PERSON>az
    }
}