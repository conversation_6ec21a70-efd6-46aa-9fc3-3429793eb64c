using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using Labsim;

public class PhlebotomyTerminalUIController : MonoBehaviour
{
    #region Component Dependencies
    [Header("UI Components")]
    [SerializeField] private UIDocument m_uiDocument;
    
    [Header("Terminal Reference")]
    [SerializeField] private PhlebotomyTerminal phlebotomyTerminal;
    #endregion

    #region UI Elements
    private VisualElement m_mainContainer;
    private Label m_patientIdLabel;
    private VisualElement m_testsList;
    private VisualElement m_noTestsMessage;
    private ScrollView m_testsScrollView;
    private Button m_printButton;
     #endregion


     private void Awake()
     {
        InitializeUI();
     }

     private void Start()
     {
        if (m_uiDocument != null)
        {
            m_uiDocument.rootVisualElement.style.display = DisplayStyle.Flex;
        }
     }

     #region UI Initialization
        private void InitializeUI()
        {
            if (m_uiDocument == null)
            {
                m_uiDocument = GetComponentInChildren<UIDocument>();
                
                if (m_uiDocument == null)
                {
                    Debug.LogError("[PhlebotomyTerminal] UIDocument component not found!");
                    return;
                }
            }

            var root = m_uiDocument.rootVisualElement;

            m_mainContainer = root.Q<VisualElement>("MainContainer");
            m_patientIdLabel = root.Q<Label>("PatientIdLabel");
            m_testsList = root.Q<VisualElement>("TestsList");
            m_noTestsMessage = root.Q<VisualElement>("NoTestsMessage");
            m_testsScrollView = root.Q<ScrollView>("TestsScrollView");
            m_printButton = root.Q<Button>("PrintButton");


            if (m_printButton != null)
            {
                m_printButton.clicked += OnPrintButtonClicked;
            }
            else
            {
                Debug.LogError("[PhlebotomyTerminalUIController] Print button not found in UXML!");
            }

            if (m_mainContainer == null)
            {
                Debug.LogError("[PhlebotomyTerminal] MainContainer not found in UXML!");
            }
        }
        #endregion

        #region Button Event Handlers
        private void OnPrintButtonClicked()
        {
            if (phlebotomyTerminal == null)
            {
                phlebotomyTerminal = GetComponentInParent<PhlebotomyTerminal>();
            }
            
            if (phlebotomyTerminal != null)
            {
                phlebotomyTerminal.OnPrintButtonClicked();
            }
            else
            {
                Debug.LogError("[PhlebotomyTerminalUIController] PhlebotomyTerminal reference not found!");
            }
        }
        #endregion

        public void DisplayPatientTests(List<Labsim.TestResultInstance> patientTests)
        {
            if (m_testsList == null)
            {
                return;
            }

            m_testsList.Clear();

            if (patientTests == null || patientTests.Count == 0)
            {
                if (m_noTestsMessage != null)
                {
                    m_noTestsMessage.style.display = DisplayStyle.Flex;
                }
                if (m_testsScrollView != null)
                {
                    m_testsScrollView.style.display = DisplayStyle.None;
                }
                return;
            }

            if (m_noTestsMessage != null)
            {
                m_noTestsMessage.style.display = DisplayStyle.None;
            }
            if (m_testsScrollView != null)
            {
                m_testsScrollView.style.display = DisplayStyle.Flex;
            }

            foreach (var test in patientTests)
            {
                var testDefinition = test.testNameID_FK != null ? 
                    Labsim.LabDatabaseManager.Instance?.GetTestDefinitionByID(test.testNameID_FK) : null;
                CreateTestItem(test, testDefinition);
            }
        }

        #region Patient Test Display        
        public void UpdatePatientInfo(string patientId)
        {
            if (m_patientIdLabel != null)
            {
                m_patientIdLabel.text = $"Patient ID: {patientId}";
            }
        }

        public void CreateTestItem(Labsim.TestResultInstance test, Labsim.TestDefinitionSO testDefinition = null)
        {
            if (test == null) return;

            string displayName = testDefinition != null ? testDefinition.displayName : test.testNameID_FK;

            var testItem = new VisualElement();
            testItem.AddToClassList("test-item");

            
            if (testDefinition != null)
            {
                var colorBox = new VisualElement();
                colorBox.AddToClassList("test-color-box");

                Color testColor = TestColorUtility.GetColorFromTestColor(testDefinition.testColor);
                colorBox.style.backgroundColor = testColor;

                testItem.Add(colorBox);
            }

            var testNameLabel = new Label(displayName);
            testNameLabel.AddToClassList("test-name");
            testItem.Add(testNameLabel);

            var testStatusLabel = new Label(test.status.ToString());
            testStatusLabel.AddToClassList("test-status");

            switch (test.status)
            {
                case Labsim.TestStatus.Requested:
                    testStatusLabel.AddToClassList("test-status-pending");
                    break;
                case Labsim.TestStatus.InProgress:
                    testStatusLabel.AddToClassList("test-status-in-progress");
                    break;
                case Labsim.TestStatus.Completed:
                    testStatusLabel.AddToClassList("test-status-completed");
                    break;
            }
            testItem.Add(testStatusLabel);

            var instanceIdLabel = new Label($"#{test.instanceTestId}");
            instanceIdLabel.AddToClassList("test-date");
            testItem.Add(instanceIdLabel);

            m_testsList.Add(testItem);
        }
        #endregion
}
