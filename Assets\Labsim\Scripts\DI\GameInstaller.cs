using UnityEngine;
using Reflex.Core;
using System.Collections.Generic;
using Labsim;

public class GameInstaller : Mono<PERSON><PERSON><PERSON><PERSON>, IInstaller
{
    [SerializeField] private List<TestDefinitionSO> testDefinitions;
    public static ILabDatabaseService DatabaseService { get; private set; }
    
    public void InstallBindings(ContainerBuilder builder)
    {
        Debug.Log("[ProjectInstaller] Installing ILabDatabaseService...");
        
        builder.AddSingleton<ILabDatabaseService>(container => 
        {
            Debug.Log($"[ProjectInstaller] Creating LabDatabaseService with {testDefinitions?.Count ?? 0} test definitions");
            var service = new LabDatabaseService(testDefinitions);
            DatabaseService = service; // Store for static access
            return service;
        });
        
        Debug.Log("[ProjectInstaller] ILabDatabaseService installed successfully");
    }
}
