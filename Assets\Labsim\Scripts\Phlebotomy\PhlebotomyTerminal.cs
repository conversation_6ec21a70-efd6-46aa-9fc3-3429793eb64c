using UnityEngine;
using PurrNet;
using UnityEngine.InputSystem;
using System;
using System.Collections.Generic;
using Labsim.Phlebotomy;
using Reflex.Attributes;

namespace Labsim
{
    //[RequireComponent(typeof(TerminalInteractor))]
    public class PhlebotomyTerminal : NetworkBehaviour
    {
        #region Dependencies
        [Header("Core Dependencies")]
        [SerializeField] private PhlebotomyTerminalUIController uiController;
        [SerializeField] private PhlebotomyTerminalCameraController cameraController;
        [SerializeField] private PhlebotomyTerminalNetworkController networkController;
        [SerializeField] private PhlebotomyTerminalInputController inputController;
        [SerializeField] private PhlebotomyTerminalDragDropController dragDropController;
        [SerializeField] private PhlebotomyTerminalPrintController printController;

        [Header("Patient Management")]
        [SerializeField] private Transform terminalWaitArea;
        [SerializeField] private int callablePatientId;
        [SerializeField] public bool isPatientInArea = false;

        [Header("Tube Spawners")]
        [SerializeField] private List<PhlebotomyTerminalTubeSpawner> tubeSpawners = new List<PhlebotomyTerminalTubeSpawner>();
        #endregion

        #region Runtime Fields
        //private TerminalInteractor terminalInteractor;
        private ThirdPersonController thirdPersonController;
        private string currentPatientId = "";
        [Inject] private ILabDatabaseService labDatabaseService;
        #endregion

        #region Events
        public static event Action<ulong, int, bool, Transform> OnPhlebotomyTerminalCall;
        public static event Action OnPatientScanned;
        public static event Action OnPrintRequested;
        public static event Action OnTerminalExited;
        #endregion

        #region Properties
        public string InteractionPrompt => "Phlebotomy Terminal";
        // public bool IsInUse => networkController?.IsInUse ?? false;
        // public ulong CurrentClientID => networkController?.CurrentClientID ?? ulong.MaxValue;
        public ThirdPersonController ThirdPersonController => thirdPersonController;
        // public TerminalInteractor TerminalInteractor => terminalInteractor;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeComponents();
            // SubscribeToEvents();
        }

        private new void OnDestroy()
        {
            base.OnDestroy();
            // UnsubscribeFromEvents();

            // if (terminalInteractor != null)
            // {
            //     terminalInteractor.OnTerminalInteractionSuccess -= OnPatientBraceletScanned;
            // }
        }
        #endregion

        #region Initialization
        private void InitializeComponents()
        {
            // terminalInteractor = GetComponent<TerminalInteractor>();
            
            if (uiController == null) uiController = GetComponent<PhlebotomyTerminalUIController>();
            if (cameraController == null) cameraController = GetComponent<PhlebotomyTerminalCameraController>();
            if (networkController == null) networkController = GetComponent<PhlebotomyTerminalNetworkController>();
            if (inputController == null) inputController = GetComponent<PhlebotomyTerminalInputController>();
            if (dragDropController == null) dragDropController = GetComponent<PhlebotomyTerminalDragDropController>();
            if (printController == null) printController = GetComponent<PhlebotomyTerminalPrintController>();

            // if (terminalInteractor != null)
            // {
            //     terminalInteractor.OnTerminalInteractionSuccess += OnPatientBraceletScanned;
            // }

            InitializePrintController();
        }

        // private void SubscribeToEvents()
        // {
        //     if (networkController != null)
        //         networkController.OnTerminalReleased += OnTerminalReleased;
                
        //     //if (inputController != null)
        //         //inputController.OnTerminalExitRequested += OnTerminalExitRequested;
        // }

        // private void UnsubscribeFromEvents()
        // {
        //     if (networkController != null)
        //         networkController.OnTerminalReleased -= OnTerminalReleased;
                
        //     //if (inputController != null)
        //         //inputController.OnTerminalExitRequested -= OnTerminalExitRequested;
        // }
        #endregion

        #region IInteractable Implementation
        // public void Interact(NetworkObject interactorObject)
        // {
        //     if (!CanInteract(interactorObject)) return;

        //     // Only allow local player's interactor to proceed
        //     if (NetworkManager.Singleton == null || interactorObject.OwnerClientId != NetworkManager.Singleton.LocalClientId)
        //         return;

        //     SetTerminalInUse(interactorObject.OwnerClientId);
        //     SetupPlayerDependencies(interactorObject);
        //     ActivateTerminalMode();
        // }
        #endregion

        #region Terminal Setup
        // private void SetupPlayerDependencies(NetworkObject interactorObject)
        // {
        //     if (!interactorObject.TryGetComponent(out thirdPersonController))
        //     {
        //         Debug.LogError($"ThirdPersonController missing on {interactorObject.name}");
        //         return;
        //     }
            
        //     if (!interactorObject.TryGetComponent(out PlayerInputHandler playerInputHandler))
        //     {
        //         Debug.LogError($"PlayerInputHandler missing on {interactorObject.name}");
        //         return;
        //     }

        //     inputSystem = playerInputHandler.GetInputSystem();
        //     if (inputSystem == null)
        //     {
        //         Debug.LogError("LabsimInputSystem is null!");
        //         return;
        //     }

        //     SetupInputSystem();
        //     SetupTerminalInteractor();
        // }

        // private void SetupInputSystem()
        // {
        //     if (inputController != null)
        //     {
        //         inputController.Initialize(inputSystem, cameraController);
        //     }
        // }

        private void SetupTerminalInteractor()
        {
            // if (terminalInteractor == null || inputController == null) return;

            // Camera terminalCamera = Camera.main ?? FindFirstObjectByType<Camera>();
            // if (terminalCamera == null) return;

            // terminalInteractor.Initialize(inputSystem, terminalCamera);
            
            // var interactAction = inputController.GetTerminalInteractAction();
            // if (interactAction != null)
            // {
            //     terminalInteractor.SetTerminalInteractAction(interactAction);
            //     terminalInteractor.EnableTerminalInteraction();
            // }
        }

        // private void ActivateTerminalMode()
        // {
        //     inputController?.DisablePlayerInputs();
            
        //     if (dragDropController != null)
        //     {
        //         dragDropController.inputSystem = inputSystem;
        //         dragDropController.enabled = true;
        //     }
            
        //     inputController?.EnableTerminalCameraControl();
        // }
        #endregion

        #region Event Handlers
        //private void OnTerminalExitRequested() => ReleaseTerminal();

        // private void OnTerminalReleased(ulong clientId)
        // {
        //     inputController?.EnablePlayerInputs();

        //     if (dragDropController != null)
        //         dragDropController.enabled = false;
        //     //terminalInteractor?.DisableTerminalInteraction();
        //     OnTerminalExited?.Invoke();
        // }

        private void OnPatientBraceletScanned(GameObject interactedObject)
        {
            if (!interactedObject.TryGetComponent(out PatientBracelet bracelet)) return;

            currentPatientId = bracelet.PatientId.ToString();
            
            var tests = labDatabaseService?.GetTestsForPatient(currentPatientId);
            
            uiController?.UpdatePatientInfo(currentPatientId);
            uiController?.DisplayPatientTests(tests);
            
            OnPatientScanned?.Invoke();
        }
        #endregion

        #region Patient Management
        public void PhlebotomyTerminalCall(ulong clientId)
        {
            OnPhlebotomyTerminalCall?.Invoke(clientId, callablePatientId, true, terminalWaitArea);
        }

        public void OnPrintButtonClicked()
        {
            OnPrintRequested?.Invoke();

            if (string.IsNullOrEmpty(currentPatientId))
            {
                Debug.LogWarning("[PhlebotomyTerminal] No patient scanned; print aborted.");
                return;
            }

            RequestPrintServerRpc(currentPatientId);
        }
        #endregion

        #region RPCs
        // [ServerRpc(RequireOwnership = false)]
        private void RequestPrintServerRpc(string patientId)
        {
            if (printController == null)
            {
                Debug.LogError("[PhlebotomyTerminal] PrintController is not available!");
                return;
            }

            if (string.IsNullOrEmpty(patientId))
            {
                Debug.LogWarning("[PhlebotomyTerminal] Empty patient id received on server; aborting print.");
                return;
            }

            printController.ExecutePrintSequence(patientId);
        }
        #endregion

        #region Print System Initialization
        private void InitializePrintController()
        {
            if (printController != null)
            {
                foreach (var tubeSpawner in tubeSpawners)
                {
                    printController.RegisterTubeSpawner(tubeSpawner);
                }
            }
        }
        #endregion

        #region Tube Spawner Management
        public void RegisterTubeSpawner(PhlebotomyTerminalTubeSpawner tubeSpawner)
        {
            if (tubeSpawner != null && !tubeSpawners.Contains(tubeSpawner))
            {
                tubeSpawners.Add(tubeSpawner);
                printController?.RegisterTubeSpawner(tubeSpawner);
            }
        }

        public void UnregisterTubeSpawner(PhlebotomyTerminalTubeSpawner tubeSpawner)
        {
            if (tubeSpawners.Remove(tubeSpawner))
            {
                printController?.UnregisterTubeSpawner(tubeSpawner);
            }
        }

        public void ClearAllTubeSpawners()
        {
            tubeSpawners.Clear();
            printController?.ClearAllTubeSpawners();
        }
        
        public List<PhlebotomyTerminalTubeSpawner> GetTubeSpawners() => new List<PhlebotomyTerminalTubeSpawner>(tubeSpawners);
        #endregion
    }
}