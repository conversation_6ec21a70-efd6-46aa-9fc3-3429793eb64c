using UnityEngine;
using System;
using PurrNet;

public class PhlebotomyTerminalInteractController : InteractableBase
{
    public static event Action<IInteractor> OnTerminalInteracted;

    protected override void HandleHoverEnter(IInteractor interactor)
    {
        Debug.Log("Hoverrr");
    }

    protected override void HandleHoverExit(IInteractor interactor)
    {
        Debug.Log("Hover exitt");
    }
    protected override void HandleInteraction(IInteractor interactor)
    {
        canInteract.value = false;
        OnTerminalInteracted?.Invoke(interactor);
   }
}
