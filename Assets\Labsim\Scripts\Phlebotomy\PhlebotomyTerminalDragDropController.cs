using UnityEngine;
using Labsim;
using UnityEngine.InputSystem;

public class PhlebotomyTerminalDragDropController : MonoBehaviour
{
    [Header("Input System")]
    public PlayerInput playerInput;
    
    [Header("Raycast Settings")]
    public LayerMask ignoreLayerMask = 0;
    
    
    private Camera cam;
    private bool isDragging = false;
    private IDragable currentDragableObject;
    private Vector3 originalPosition;
    
    
    private void Start()
    {
        cam = Camera.main;
    }
    
    private void OnEnable()
    {
        if (playerInput != null)
        {
            playerInput.PhlebotomyTerminal.PointerPosition.Enable();
            playerInput.PhlebotomyTerminal.Click.Enable();
            playerInput.PhlebotomyTerminal.Click.performed += OnClickStart;
            playerInput.PhlebotomyTerminal.Click.canceled += OnClickEnd;
        }
    }
    
    private void OnDisable()
    {
        if (playerInput != null)
        {
            playerInput.PhlebotomyTerminal.Click.performed -= OnClickStart;
            playerInput.PhlebotomyTerminal.Click.canceled -= OnClickEnd;
        }
    }
    
    private void Update()
    {
        // DISABLED: This component is not used, prevent NaN errors
        return;
        
        if (isDragging && cam != null && currentDragableObject != null && playerInput != null)
        {
            Vector2 mousePos = playerInput.PhlebotomyTerminal.PointerPosition.ReadValue<Vector2>();
            
            // Check for valid mouse position before using it
            if (!float.IsNaN(mousePos.x) && !float.IsNaN(mousePos.y))
            {
                Ray ray = cam.ScreenPointToRay(mousePos);
                
                RaycastHit hit;
                if (Physics.Raycast(ray, out hit, Mathf.Infinity, ~ignoreLayerMask))
                {
                    currentDragableObject.OnDrag(hit.point);
                }
            }
        }
    }
    
    private void OnClickStart(InputAction.CallbackContext context)
    {
        // DISABLED: This component is not used
        return;
        
        if (playerInput == null || cam == null) return;
        
        Vector2 mousePos = playerInput.PhlebotomyTerminal.PointerPosition.ReadValue<Vector2>();
        
        // Check for valid mouse position before using it
        if (float.IsNaN(mousePos.x) || float.IsNaN(mousePos.y)) return;
        
        Ray ray = cam.ScreenPointToRay(mousePos);
        
        RaycastHit hit;
        if (Physics.Raycast(ray, out hit))
        {
            IDragable dragableObject = hit.collider.GetComponent<IDragable>();
            if (dragableObject != null)
            {
                currentDragableObject = dragableObject;
                originalPosition = currentDragableObject.GetRigidbody().position;
                currentDragableObject.OnDragStart(hit.point);
                isDragging = true;
            }
        }
    }
    
    private void OnClickEnd(InputAction.CallbackContext context)
    {
        if (isDragging && currentDragableObject != null)
        {
            if (currentDragableObject.IsOverValidDropZone && currentDragableObject.CurrentDropZone != null)
            {
                // Successful drop - handle SampleTube highlight and return TestBarcode to pool
                var dropZone = currentDragableObject.CurrentDropZone;
                dropZone.OnDrop(currentDragableObject);
                
                // Manually reset SampleTube highlight before returning to pool
                if (dropZone is SampleTube sampleTube)
                {
                    sampleTube.ResetHighlight();
                }
                
                // Return to pool if it's a TestBarcode
                if (currentDragableObject is TestBarcode testBarcode)
                {
                    testBarcode.ReturnToPool();
                }
            }
            else
            {
                
                currentDragableObject.GetRigidbody().MovePosition(originalPosition);
                currentDragableObject.OnDragEnd(originalPosition);
            }
            
            
            isDragging = false;
            currentDragableObject = null;
        }
    }
    
    
}