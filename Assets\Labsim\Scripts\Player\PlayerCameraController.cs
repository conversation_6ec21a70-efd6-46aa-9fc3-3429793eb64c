using UnityEngine;
using PurrNet;  
using Unity.Cinemachine;
#if ENABLE_INPUT_SYSTEM 
using UnityEngine.InputSystem;
#endif

public class PlayerCameraController : NetworkBehaviour
{
    [Header("Cinemachine")]
    public CinemachineCamera playerFollowCamera;
    
    public Transform CinemachineCameraTarget;

    //references
    [SerializeField] private PlayerInputHandler playerInputHandler;

    public float TopClamp = 70.0f;

    public float BottomClamp = -30.0f;
    public float CameraAngleOverride = 0.0f;
    public bool LockCameraPosition = false;
    
    private float _cinemachineTargetYaw;
    private float _cinemachineTargetPitch;
    

    private const float _threshold = 0.01f;


    void Start()
    {
        playerInputHandler = GetComponent<PlayerInputHandler>();
    }

    protected override void OnSpawned()
    {
        base.OnSpawned();

        enabled = isOwner;
        playerFollowCamera.gameObject.SetActive(isOwner);

        if (isOwner)
        {
            if (CinemachineCameraTarget != null)
            {
                _cinemachineTargetYaw = CinemachineCameraTarget.transform.rotation.eulerAngles.y;
            }
        }
    }
    
    private void LateUpdate()
    {
        if (isOwner)
        {
            CameraRotation();
        }
    }
    
    private void CameraRotation()
    {   
        if (CinemachineCameraTarget == null) 
        {
            Debug.LogWarning("PlayerCameraController: CinemachineCameraTarget is null!");
            return;
        }
        
        
        
        Vector2 lookInput = playerInputHandler.playerInput.Player.Look.ReadValue<Vector2>();
        
        
        if (lookInput.sqrMagnitude >= _threshold && !LockCameraPosition)
        {
            //Don't multiply mouse input by Time.deltaTime;
            //float deltaTimeMultiplier = IsCurrentDeviceMouse ? 1.0f : Time.deltaTime;
            float deltaTimeMultiplier = 1.0f;

            _cinemachineTargetYaw += lookInput.x * deltaTimeMultiplier;
            _cinemachineTargetPitch += lookInput.y * deltaTimeMultiplier;
        }

        // clamp our rotations so our values are limited 360 degrees
        _cinemachineTargetYaw = ClampAngle(_cinemachineTargetYaw, float.MinValue, float.MaxValue);
        _cinemachineTargetPitch = ClampAngle(_cinemachineTargetPitch, BottomClamp, TopClamp);


            // --- YENİ KONTROL KODU BAŞLANGICI ---
        // NaN değerinin oluşup oluşmadığını burada kontrol edelim.
        if (float.IsNaN(_cinemachineTargetYaw) || float.IsNaN(_cinemachineTargetPitch))
        {
            Debug.LogError("PlayerCameraController İÇİNDE NaN OLUŞTU! Yaw: " + _cinemachineTargetYaw + ", Pitch: " + _cinemachineTargetPitch);
            // Hata oluşursa, bu frame'de rotasyonu atamayıp çıkalım.
            return; 
        }
        // --- YENİ KONTROL KODU BİTİŞİ ---


        // Cinemachine will follow this target
        CinemachineCameraTarget.transform.rotation = Quaternion.Euler(_cinemachineTargetPitch + CameraAngleOverride,
            _cinemachineTargetYaw, 0.0f);
    }
    
    private static float ClampAngle(float lfAngle, float lfMin, float lfMax)
    {
        if (lfAngle < -360f) lfAngle += 360f;
        if (lfAngle > 360f) lfAngle -= 360f;
        return Mathf.Clamp(lfAngle, lfMin, lfMax);
    }
}