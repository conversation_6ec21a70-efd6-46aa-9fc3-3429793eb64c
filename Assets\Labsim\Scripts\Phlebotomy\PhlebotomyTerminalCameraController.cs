using UnityEngine;
using PurrNet;
using Unity.Cinemachine;
using UnityEngine.InputSystem;
using Labsim;

namespace Labsim
{
    public enum TerminalCameraMode
    {
        FirstPerson,
        Fixed
    }

    public class PhlebotomyTerminalCameraController : NetworkBehaviour
    {
        [Header("Terminal Camera Control")]
        [SerializeField] private CinemachineCamera mainTerminalCamera;
        [SerializeField] private int activeCameraPriority = 15;

        [SerializeField] private GameObject mainTerminalCameraTarget;

        [Header("Phlebotomy Specific Cameras")]
        [SerializeField] private CinemachineCamera terminalScreenCamera;
        [SerializeField] private CinemachineCamera terminalMatchCamera;

        [Header("Camera Mode Settings")]
        [SerializeField] private TerminalCameraMode m_cameraMode = TerminalCameraMode.FirstPerson;
        [SerializeField] private Vector3 m_fixedCameraRotation = Vector3.zero;
        [SerializeField] private bool m_allowCameraModeSwitch = true;

        [Header("First-Person Camera Control")]
        [SerializeField] private float m_lookSensitivity = 2f;
        [SerializeField] private float m_pitchClampMin = -60f; 
        [SerializeField] private float m_pitchClampMax = 60f;  
        [SerializeField] private float m_yawClampMin = -125f;  
        [SerializeField] private float m_yawClampMax = 0f;  
        [SerializeField] private bool m_smoothRotation = true;
        [SerializeField] private float m_rotationSmoothness = 10f;
        [SerializeField] private bool m_invertYAxis = false;


        //references
        public PlayerInputHandler playerInputHandler;

        private float m_currentPitch = 0f;
        private float m_currentYaw = 0f;
        private Vector2 m_lookInput;
        private bool m_isCameraControlActive = false;
        private TerminalCameraMode m_currentCameraMode;
        private CursorLockMode m_previousCursorLockMode;
        private bool m_previousCursorVisible;
        
    


        public bool IsCameraControlActive => m_isCameraControlActive;
        public CinemachineCamera TerminalCamera => mainTerminalCamera;
        public TerminalCameraMode CurrentCameraMode => m_currentCameraMode;
        public bool AllowCameraModeSwitch => m_allowCameraModeSwitch;
        
        protected override void OnSpawned()
        {
            base.OnSpawned();
            m_currentCameraMode = m_cameraMode;
            SubscribeToPhlebotomyEvents();
        }

        protected override void OnDespawned()
        {
            UnsubscribeFromPhlebotomyEvents();
        }

        private void Update()
        {
            if (!m_isCameraControlActive)
                return;

            HandleCameraInput();
        }

        private void HandleCameraInput()
        {
            
            if (m_currentCameraMode == TerminalCameraMode.FirstPerson)
            {
                HandleFirstPersonCameraControl();
            }
        }

        private void HandleFirstPersonCameraControl()
        {
            m_lookInput =  playerInputHandler.playerInput.PhlebotomyTerminal.TerminalLook.ReadValue<Vector2>();

            // Check for NaN in input values
            if (float.IsNaN(m_lookInput.x) || float.IsNaN(m_lookInput.y))
            {
                Debug.LogError($"[PhlebotomyTerminalCameraController] NaN detected in look input! Input: {m_lookInput}");
                return;
            }

            float mouseX = m_lookInput.x * m_lookSensitivity * Time.deltaTime;
            float mouseY = m_lookInput.y * m_lookSensitivity * Time.deltaTime;

            // Check for NaN in calculated mouse values
            if (float.IsNaN(mouseX) || float.IsNaN(mouseY))
            {
                Debug.LogError($"[PhlebotomyTerminalCameraController] NaN detected in mouse calculations! MouseX: {mouseX}, MouseY: {mouseY}, Sensitivity: {m_lookSensitivity}, DeltaTime: {Time.deltaTime}");
                return;
            }

            m_currentYaw += mouseX;
            m_currentYaw = Mathf.Clamp(m_currentYaw, m_yawClampMin, m_yawClampMax);

            if (m_invertYAxis)
            {
                m_currentPitch -= mouseY;
            }
            else
            {
                m_currentPitch += mouseY;
            }
            m_currentPitch = Mathf.Clamp(m_currentPitch, m_pitchClampMin, m_pitchClampMax);

            // Final check for NaN in rotation values before applying
            if (float.IsNaN(m_currentYaw) || float.IsNaN(m_currentPitch))
            {
                Debug.LogError($"[PhlebotomyTerminalCameraController] NaN detected in final rotation values! Yaw: {m_currentYaw}, Pitch: {m_currentPitch}");
                return;
            }

            ApplyRotationToTarget();
        }

        private void ApplyRotationToTarget()
        {
            if (mainTerminalCameraTarget == null) return;

            Quaternion targetLocalRotation;

            if (m_currentCameraMode == TerminalCameraMode.FirstPerson)
            {
                // Check for NaN values before creating quaternion
                if (float.IsNaN(m_currentPitch) || float.IsNaN(m_currentYaw))
                {
                    Debug.LogError($"[PhlebotomyTerminalCameraController] NaN detected in rotation values! Pitch: {m_currentPitch}, Yaw: {m_currentYaw}");
                    return;
                }

                targetLocalRotation = Quaternion.Euler(m_currentPitch, m_currentYaw, 0f);
            }
            else
            {
                // Check for NaN values in fixed camera rotation
                if (float.IsNaN(m_fixedCameraRotation.x) || float.IsNaN(m_fixedCameraRotation.y) || float.IsNaN(m_fixedCameraRotation.z))
                {
                    Debug.LogError($"[PhlebotomyTerminalCameraController] NaN detected in fixed camera rotation! Rotation: {m_fixedCameraRotation}");
                    return;
                }

                targetLocalRotation = Quaternion.Euler(m_fixedCameraRotation);
            }

            // Additional check for the resulting quaternion
            if (float.IsNaN(targetLocalRotation.x) || float.IsNaN(targetLocalRotation.y) ||
                float.IsNaN(targetLocalRotation.z) || float.IsNaN(targetLocalRotation.w))
            {
                Debug.LogError($"[PhlebotomyTerminalCameraController] NaN detected in target quaternion! Quaternion: {targetLocalRotation}");
                return;
            }

            if (m_smoothRotation)
            {
                mainTerminalCameraTarget.transform.localRotation = Quaternion.Slerp(
                    mainTerminalCameraTarget.transform.localRotation,
                    targetLocalRotation,
                    m_rotationSmoothness * Time.deltaTime
                );
            }
            else
            {
                mainTerminalCameraTarget.transform.localRotation = targetLocalRotation;
            }
        }

        public void EnableCameraControl()
        {
            m_isCameraControlActive = true;
            
            
            m_previousCursorLockMode = Cursor.lockState;
            m_previousCursorVisible = Cursor.visible;
            
           
            SetCursorStateForCameraMode(m_currentCameraMode);
            
            
            InitializeCameraRotation();

            
            if (mainTerminalCamera != null)
            {
                mainTerminalCamera.Priority = activeCameraPriority;
            }
            else
            {
                Debug.LogError($"[TerminalCameraController] Terminal camera is null! Please assign it in the inspector.");
            }
        }

        public void DisableCameraControl()
        {

            m_isCameraControlActive = false;
            
            Cursor.lockState = m_previousCursorLockMode;
            Cursor.visible = m_previousCursorVisible;

            if (mainTerminalCamera != null)
            {
                mainTerminalCamera.Priority = 0;
            }
        }

        public void SetCameraMode(TerminalCameraMode _mode)
        {
            if (m_currentCameraMode == _mode) return;

            m_currentCameraMode = _mode;
            
            if (m_isCameraControlActive)
            {
                SetCursorStateForCameraMode(_mode);
                ApplyRotationToTarget();
            }
        }

        public void ToggleCameraMode()
        {
            if (!m_allowCameraModeSwitch) return;

            TerminalCameraMode newMode = m_currentCameraMode == TerminalCameraMode.FirstPerson 
                ? TerminalCameraMode.Fixed 
                : TerminalCameraMode.FirstPerson;
            
            SetCameraMode(newMode);
        }

        public void SetCameraTarget(GameObject _target)
        {
            mainTerminalCameraTarget = _target;
        }

        public void SetCameraPriority(int _priority)
        {
            activeCameraPriority = _priority;
            if (mainTerminalCamera != null && m_isCameraControlActive)
            {
                mainTerminalCamera.Priority = activeCameraPriority;
            }
        }

        public void SetFixedCameraRotation(Vector3 _rotation)
        {
            m_fixedCameraRotation = _rotation;
            if (m_currentCameraMode == TerminalCameraMode.Fixed && m_isCameraControlActive)
            {
                ApplyRotationToTarget();
            }
        }

        public void SwitchCamera(CinemachineCamera _camera, TerminalCameraMode _cameraMode)
        {
            mainTerminalCamera.Priority = 0;
            _camera.Priority = activeCameraPriority;
            SetCameraMode(_cameraMode);
            mainTerminalCamera = _camera;
        }

        public void SwitchMainTerminalCamera()
        {
            mainTerminalCamera.Priority = activeCameraPriority;
        }
        private void InitializeCameraRotation()
        {
            if (mainTerminalCameraTarget == null) return;

            if (m_currentCameraMode == TerminalCameraMode.FirstPerson)
            {
                Vector3 localEulerAngles = mainTerminalCameraTarget.transform.localRotation.eulerAngles;

                // Check for NaN in the source rotation
                if (float.IsNaN(localEulerAngles.x) || float.IsNaN(localEulerAngles.y) || float.IsNaN(localEulerAngles.z))
                {
                    Debug.LogError($"[PhlebotomyTerminalCameraController] NaN detected in camera target rotation! Euler angles: {localEulerAngles}");
                    // Initialize with safe default values
                    m_currentYaw = 0f;
                    m_currentPitch = 0f;
                }
                else
                {
                    m_currentYaw = localEulerAngles.y;
                    m_currentPitch = localEulerAngles.x;

                    // Normalize angles to -180 to 180 range
                    if (m_currentPitch > 180f)
                        m_currentPitch -= 360f;

                    if (m_currentYaw > 180f)
                        m_currentYaw -= 360f;

                    // Apply clamps
                    m_currentPitch = Mathf.Clamp(m_currentPitch, m_pitchClampMin, m_pitchClampMax);
                    m_currentYaw = Mathf.Clamp(m_currentYaw, m_yawClampMin, m_yawClampMax);
                }
            }

            ApplyRotationToTarget();
        }

        private void SetCursorStateForCameraMode(TerminalCameraMode _mode)
        {
            if (_mode == TerminalCameraMode.FirstPerson)
            {
               
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
            else
            {
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
            }
        }
        private void SubscribeToPhlebotomyEvents()
        {
            PhlebotomyTerminal.OnPatientScanned += HandlePatientScanned;
            PhlebotomyTerminal.OnPrintRequested += HandlePrintRequested;
            PhlebotomyTerminal.OnTerminalExited += HandleTerminalExited;
            PhlebotomyTerminalInteractController.OnTerminalInteracted += HandleTerminalInteracted;
        }

        private void UnsubscribeFromPhlebotomyEvents()
        {
            PhlebotomyTerminal.OnPatientScanned -= HandlePatientScanned;
            PhlebotomyTerminal.OnPrintRequested -= HandlePrintRequested;
            PhlebotomyTerminal.OnTerminalExited -= HandleTerminalExited;
            PhlebotomyTerminalInteractController.OnTerminalInteracted -= HandleTerminalInteracted;
        }

        private void HandlePatientScanned()
        {
            if (terminalScreenCamera != null)
            {
                SwitchCamera(terminalScreenCamera, TerminalCameraMode.Fixed);
                Debug.Log("[PhlebotomyTerminalCameraController] Switched to screen camera for patient info");
            }
        }

        private void HandlePrintRequested()
        {
            if (terminalMatchCamera != null)
            {
                SwitchCamera(terminalMatchCamera, TerminalCameraMode.Fixed);
            }
            else
            {
                Debug.LogError("[PhlebotomyTerminalCameraController] terminalMatchCamera is null!");
            }
        }

        private void HandleTerminalExited()
        {
            if (mainTerminalCamera != null)
            {
                SwitchCamera(mainTerminalCamera, TerminalCameraMode.FirstPerson);
                Debug.Log("[PhlebotomyTerminalCameraController] Switched back to main terminal camera");
            }
        }

        private void HandleTerminalInteracted(IInteractor interactor)
        {
            EnableCameraControl();
            SwitchMainTerminalCamera();
        }
    }
}