using UnityEngine;
using PurrNet;
using Labsim;
using CrashKonijn.Agent.Core;
using CrashKonijn.Agent.Runtime;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Goals;
using Labsim.AI.GOAP.Behaviours;
using UnityEngine.Animations.Rigging;
using Labsim.AI.GOAP.Actions;
using System.Collections.Generic;
using Reflex.Core;


public class Patient : NetworkBehaviour
{
    private AgentBehaviour agent;
    private GoapActionProvider provider;
    private GoapBehaviour goap;
    private ILabDatabaseService _labDatabaseService;
    

    public bool isCalled = false;
    public bool isBloodDraw = false;
    
    [HideInInspector] public Transform targetTransform;

    public Transform bloodDrawLocation;


    public Rig handRig;

    
    
    // [Header("Network Data")]
    // public NetworkVariable<int> patientId = new NetworkVariable<int>(
    // 0,
    // NetworkVariableReadPermission.Everyone,
    // NetworkVariableWritePermission.Server
    // );


    private List<int> _activeTestInstanceIDs = new List<int>();
    public List<int> ActiveTestInstanceIDs => _activeTestInstanceIDs;

    public string InteractionPrompt => "Patient";
    public string TerminalInteractionPrompt => "Select Patient";

    [Header("Test Settings")]
    public int minRandomTestsToAssign = 2; 
    public int maxRandomTestsToAssign = 3;


    private PatientBracelet bracelet;

    public void Initialize(ILabDatabaseService labDatabaseService)
    {
        _labDatabaseService = labDatabaseService;
    }

    private void Awake()
    {
        goap = FindObjectOfType<GoapBehaviour>();
        agent = GetComponent<AgentBehaviour>();
        provider = GetComponent<GoapActionProvider>();
        handRig = GetComponentInChildren<Rig>();
        bracelet = GetComponentInChildren<PatientBracelet>();
        //bloodDrawLocation = GameObject.FindGameObjectWithTag("BloodDrawSitArea").transform;

        // This only applies sto the code demo
        if (provider.AgentTypeBehaviour == null)
            provider.AgentType = goap.GetAgentType("PatientAgent");
    }

    // public override void OnNetworkSpawn()
    // {
    //     base.OnNetworkSpawn();

    //     if (!IsServer) return;
    //     provider.RequestGoal<IdleGoal>();
    //     agent.Events.OnActionEnd += OnActionEnd;
    //     PhlebotomyTerminal.OnPhlebotomyTerminalCall -= OnTerminalCall;
    //     PhlebotomyTerminal.OnPhlebotomyTerminalCall += OnTerminalCall;

    //     if (_labDatabaseService == null)
    //     {
    //         Debug.LogError("LabDatabaseService is not initialized! Make sure Initialize() is called before OnNetworkSpawn.");
    //         return;
    //     }

    //     patientId.OnValueChanged += (oldValue, newValue) => {
    //          AddRandomTests(newValue);
    //          bracelet.PatientId = newValue;
    //     }; 

    // }



 
    //  public override void OnNetworkDespawn()
    // {
    //     if (!IsServer) return;
    //     agent.Events.OnActionEnd -= OnActionEnd;
    //     PhlebotomyTerminal.OnPhlebotomyTerminalCall -= OnTerminalCall;

    //     base.OnNetworkDespawn();
    // }

     

    private void OnActionEnd(IAction action)
    {
        if (action is ReachPhlebotomyAction)
        {
            Debug.Log("ReachPhlebotomyGoal");
            if(isBloodDraw)
            {
                provider.RequestGoal<ReachBloodDrawGoal>();
                return;
            }
        }
        
        // if (this.data.hunger > 50)
        // {
        //     this.provider.RequestGoal<EatGoal>();
        //     return;
        // }
        this.provider.RequestGoal<IdleGoal>();
        
    }

    // private void OnTerminalCall(ulong terminalId, int targetAgentId, bool isActive, Transform terminalTransform)
    // {
    //     if (this == null || targetAgentId != patientId.Value)
    //     {
    //         return;
    //     }

    //     Debug.Log("Patient ID: " + patientId.Value);
    //     if (isActive)
    //     {
    //         isCalled = isActive;
    //         targetTransform = terminalTransform;

            
    //         agent.StopAction();
        
    //         provider.RequestGoal<ReachPhlebotomyGoal>();
    //     }
    //     else
    //     {
          
    //     }

    // }

    private void AddRandomTests(int patientid)
    {
        _activeTestInstanceIDs.Clear(); 

        List<string> availableTestDefIDs = _labDatabaseService.GetAvailableTestDefinitionIDs();

        if (availableTestDefIDs == null || availableTestDefIDs.Count == 0)
        {
            Debug.LogWarning($"No available test definitions found in LabDatabaseManager. Patient {patientid} will not have any tests assigned initially.");
            return;
        }

        int numberOfTestsToAssign = Random.Range(minRandomTestsToAssign, maxRandomTestsToAssign + 1);
        numberOfTestsToAssign = Mathf.Min(numberOfTestsToAssign, availableTestDefIDs.Count); 

       


        List<string> selectableTestDefIDs = new List<string>(availableTestDefIDs);

        for (int i = 0; i < numberOfTestsToAssign; i++)
        {
            if (selectableTestDefIDs.Count == 0) break; 

            int randomIndex = Random.Range(0, selectableTestDefIDs.Count);
            string randomTestDefID = selectableTestDefIDs[randomIndex];
            selectableTestDefIDs.RemoveAt(randomIndex); 

            TestResultInstance addedTest = _labDatabaseService.AddTestToPatient(patientid.ToString(), randomTestDefID);

            if (addedTest != null)
            {
                _activeTestInstanceIDs.Add(addedTest.instanceTestId);
            }
            else
            {
                Debug.LogWarning($"Patient {patientid}: Failed to add test with definition ID '{randomTestDefID}'.");
            }
        }
    }

    // public void Interact(NetworkObject interactorObject)
    // {
    //     Debug.Log("PATIENT INTERACT");
    // }

    // public void TerminalInteract(NetworkObject interactorObject)
    // {
    //     Debug.Log("PATIENT TERMINAL INTERACT");
    //     isBloodDraw = false;

    //     // Handle patient selection in terminal
    //     // This could trigger UI updates, patient data display, etc.
    // }
}